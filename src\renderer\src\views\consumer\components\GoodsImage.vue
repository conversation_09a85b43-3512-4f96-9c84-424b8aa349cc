<template>
  <div class="common-image-container rounded-lg" :style="containerStyle">
    <slot></slot>
    <img
      :src="actualSrc"
      :alt="alt"
      :style="imageStyle"
      class="common-image"
      @error="handleImageError"
      @load="handleImageLoad"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType, watch } from 'vue'
import defaultImage from './imgs/default.png'

// 定义组件属性
const props = defineProps({
  // 图片源
  src: {
    type: String,
    required: true,
  },
  // 默认图片源
  defaultSrc: {
    type: String,
    default: defaultImage,
  },
  // 图片描述
  alt: {
    type: String,
    default: '图片',
  },
  // 宽度
  width: {
    type: [Number, String],
    default: 'auto',
  },
  // 高度
  height: {
    type: [Number, String],
    default: 'auto',
  },
  // 填充模式
  objectFit: {
    type: String as PropType<'fill' | 'contain' | 'cover' | 'none' | 'scale-down'>,
    default: 'cover',
  },
})

// 控制实际显示的图片源
const actualSrc = ref(props.src)
const isLoading = ref(true)
const hasError = ref(false)

watch(
  () => props.src,
  (newVal) => (actualSrc.value = newVal),
  { immediate: true },
)

// 处理图片加载错误
const handleImageError = () => {
  hasError.value = true
  actualSrc.value = props.defaultSrc
}

// 处理图片加载完成
const handleImageLoad = () => {
  isLoading.value = false
}

// 计算样式
const containerStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    position: 'relative' as const,
  }
})

const imageStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    objectFit: props.objectFit,
  }
})
</script>

<style scoped>
.common-image-container {
  overflow: hidden;
  position: relative;
}

.common-image {
  display: block;
  transition: opacity 0.3s ease;
}
</style>
