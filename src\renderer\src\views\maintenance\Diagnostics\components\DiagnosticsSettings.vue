<template>
  <div class="settings-container">
    <h3>配置管理</h3>
    <div class="settings-row">
      <div class="setting-item">
        <label>配置模型</label>
        <select>
          <option>标准模型</option>
          <option>高级模型</option>
          <option>自定义模型</option>
        </select>
      </div>
      <div class="setting-item">
        <label>网络模式</label>
        <select>
          <option>默认模式</option>
          <option>兼容模式</option>
          <option>离线模式</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置相关逻辑
</script>

<style scoped>
.settings-container {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
}

h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.settings-row {
  display: flex;
  gap: 20px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

label {
  font-weight: bold;
}

select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}
</style>
