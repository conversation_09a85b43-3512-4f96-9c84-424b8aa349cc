import { ref, computed, type Ref } from 'vue'
import { useSocket, resetWebSocket } from './useSocket'

// 本地存储的键名
const STORAGE_KEY = 'websocket_address'

// 默认WebSocket地址
const DEFAULT_WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080'

// 测试结果类型
interface TestResult {
  type: 'success' | 'error'
  message: string
}

// 从本地存储获取地址
const getStoredAddress = (): string => {
  try {
    return localStorage.getItem(STORAGE_KEY) || DEFAULT_WS_URL
  } catch {
    return DEFAULT_WS_URL
  }
}

// 保存地址到本地存储
const saveAddress = (address: string): void => {
  try {
    localStorage.setItem(STORAGE_KEY, address)
  } catch (error) {
    console.warn('无法保存WebSocket地址到本地存储:', error)
  }
}

// 全局状态
const currentWsAddress: Ref<string> = ref(getStoredAddress())
const testing = ref(false)

// 获取当前WebSocket实例（每次都重新获取，确保状态同步）
const getCurrentWebSocketInstance = () => {
  try {
    return useSocket(currentWsAddress.value, {
      autoReconnect: {
        retries: 3,
        delay: 2000,
        onFailed: () => {
          console.log('WebSocket重连失败')
        },
      },
      heartbeat: {
        message: 'ping',
        interval: 30000,
        pongTimeout: 5000,
      },
      onConnected: () => {
        console.log('WebSocket连接成功:', currentWsAddress.value)
      },
      onDisconnected: (_, event) => {
        console.log('WebSocket连接断开:', event.reason)
      },
      onError: () => {
        console.error('WebSocket连接错误')
      },
      onMessage: (_, event) => {
        console.log('WebSocket收到消息:', event.data)
      },
    })
  } catch (error) {
    console.error('获取WebSocket实例失败:', error)
    return null
  }
}

// 测试WebSocket连接
const testConnection = (url: string): Promise<TestResult> => {
  return new Promise((resolve) => {
    testing.value = true
    let testWs: WebSocket | null = null
    let timeoutId: NodeJS.Timeout | null = null

    const cleanup = () => {
      testing.value = false
      if (testWs) {
        testWs.close()
        testWs = null
      }
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }

    try {
      testWs = new WebSocket(url)

      // 设置连接超时
      timeoutId = setTimeout(() => {
        cleanup()
        resolve({
          type: 'error',
          message: '连接超时（10秒）',
        })
      }, 10000)

      testWs.onopen = () => {
        cleanup()
        resolve({
          type: 'success',
          message: '连接测试成功',
        })
      }

      testWs.onerror = () => {
        cleanup()
        resolve({
          type: 'error',
          message: '连接失败，请检查地址是否正确',
        })
      }

      testWs.onclose = (event) => {
        if (event.code !== 1000) {
          cleanup()
          resolve({
            type: 'error',
            message: `连接被拒绝 (${event.code}: ${event.reason || '未知原因'})`,
          })
        }
      }
    } catch {
      cleanup()
      resolve({
        type: 'error',
        message: '无效的WebSocket地址格式',
      })
    }
  })
}

// 更新WebSocket地址
const updateAddress = (newAddress: string): void => {
  // 关闭当前连接
  resetWebSocket()

  // 更新地址
  currentWsAddress.value = newAddress
  saveAddress(newAddress)

  // 重新获取实例会自动连接新地址
  getCurrentWebSocketInstance()
}

// 获取连接状态
const getConnectionStatus = () => {
  const instance = getCurrentWebSocketInstance()
  return instance?.status || ref('CLOSED')
}

// WebSocket配置hooks返回类型
interface UseWebSocketConfigReturn {
  currentAddress: Ref<string>
  status: Ref<string>
  testConnection: (url: string) => Promise<TestResult>
  updateAddress: (newAddress: string) => void
  testing: Ref<boolean>
  getSocket: () => ReturnType<typeof useSocket> | null
  send: (message: string) => boolean
  reset: () => void
}

// WebSocket配置hooks
export function useWebSocketConfig(): UseWebSocketConfigReturn {
  return {
    // 当前地址
    currentAddress: computed(() => currentWsAddress.value),

    // 连接状态
    status: getConnectionStatus(),

    // 测试连接
    testConnection,

    // 更新地址
    updateAddress,

    // 测试状态
    testing: computed(() => testing.value),

    // 获取WebSocket实例
    getSocket: getCurrentWebSocketInstance,

    // 发送消息
    send: (message: string) => {
      const instance = getCurrentWebSocketInstance()
      if (instance) {
        return instance.send(message)
      }
      return false
    },

    // 重置连接
    reset: () => {
      resetWebSocket()
      // 重新获取实例
      getCurrentWebSocketInstance()
    },
  }
}
