<template>
  <div class="category-tabs overflow-x-auto whitespace-nowrap h-[60px] -mx-1">
    <div
      v-for="(category, index) in categories"
      :key="index"
      class="category-tab inline-block px-4 py-2 mx-1 text-center cursor-pointer transition-all flex items-center justify-center"
      :class="{ 'active-category': activeIndex === index }"
      @click="handleCategoryClick(index)"
    >
      <div class="mt-1.5">
        {{ category.typeName === '全部' ? `&emsp;全部&emsp;` : category.typeName }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, nextTick } from 'vue'
import type { GoodsItem } from '@/views/consumer/goods-menu/types'

interface Category {
  // 排序
  sort: number
  // 类型名称
  typeName: string
  // 类型
  type: string
  // 商品列表
  goodsMenuVOList: GoodsItem[]
}

const props = defineProps<{
  categories: Category[]
  activeIndex: number
}>()

const emit = defineEmits<{
  (e: 'update:activeIndex', index: number): void
}>()

// 处理分类点击
const handleCategoryClick = (index: number) => {
  emit('update:activeIndex', index)
  scrollIntoView(index)
}

// 确保选中的分类在可视区域内
const scrollIntoView = async (index: number) => {
  await nextTick()
  const container = document.querySelector('.category-tabs')
  const tabs = container?.querySelectorAll('.category-tab')

  if (!container || !tabs || tabs.length === 0) return

  const activeTab = tabs[index] as HTMLElement
  if (!activeTab) return

  const tabLeft = activeTab.offsetLeft
  const tabRight = tabLeft + activeTab.offsetWidth
  const containerWidth = container.clientWidth
  const scrollLeft = container.scrollLeft

  if (tabRight > scrollLeft + containerWidth) {
    container.scrollLeft = tabRight - containerWidth + 20
  } else if (tabLeft < scrollLeft) {
    container.scrollLeft = tabLeft - 20
  }
}

// 监听选中分类的变化
watch(
  () => props.activeIndex,
  (newIndex) => {
    scrollIntoView(newIndex)
  },
)

// 监听分类数据变化
watch(
  () => props.categories,
  () => {
    scrollIntoView(props.activeIndex)
  },
  { deep: true },
)

onMounted(() => {
  // 初始化滚动位置
  scrollIntoView(props.activeIndex)

  // 监听窗口大小变化
  window.addEventListener('resize', () => scrollIntoView(props.activeIndex))
})
</script>

<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>

<style scoped>
.category-tabs {
  scrollbar-width: none;
  position: relative;
}
.category-tabs::-webkit-scrollbar {
  display: none;
}

.category-tab {
  position: relative;
  color: #1a1a1a;
  font-size: 20px;
  border-radius: 10px;
  height: 60px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.category-tab.active-category {
  color: #6d3202;
  font-weight: bold;
  background-color: rgba(109, 50, 2, 0.1);
}
</style>
