<template>
  <div class="h-full overflow-hidden relative">
    <BackWidget ref="backWidget" />
    <div class="flex flex-col justify-between h-full">
      <div v-if="items.length" class="px-2 box-border mb-auto">
        <div class="p-10 flex flex-col gap-4 rounded-lg bg-white justify-center items-center">
          <Swiper v-bind="swiperOptions" :modules="[FreeMode, Scrollbar, Mousewheel]">
            <SwiperSlide>
              <template v-for="(item, index) in [...items,..]" :key="item.id">
                <GoodsItem
                  :goods="item"
                  referrer="cart"
                  style="--goods-item-width: 160px; --goods-item-height: 160px; margin: auto"
                />
                <div
                  v-if="index !== items.length - 1"
                  class="h-px bg-[#E9E7E5] w-[600px] m-auto my-4"
                ></div>
              </template>
            </SwiperSlide>
          </Swiper>
        </div>
      </div>

      <div
        class="flex justify-center bg-white w-full mt-auto py-5 px-8 border-t border-[#e9e7e5] border-solid"
      >
        <div
          class="flex items-center mr-auto transition-all duration-200 active:scale-[0.95]"
          @click="handleClearCart"
        >
          <img :src="IconClearCart" class="w-[40px] h-[40px]" />
          <span class="text-[20px] text-[#F25555]">清空购物车</span>
        </div>
        <div class="bg-white flex items-center w-[600px] mr-auto -ml-[140px]">
          <div class="flex-1 inline-flex gap-2 items-center text-sm text-gray-900">
            <span class="text-[20px]">共{{ totalCount }}件</span>
            <div class="flex-1 flex items-center">
              <span class="text-[20px]">应付:</span>
              <span class="text-lg font-bold text-[#6D3202] ml-1 -mt-2">
                <span class="text-[24px] mr-1">¥</span>
                <span class="text-[36px]">{{ computedPrice?.payPrice }}</span>
              </span>
              <span
                v-if="computedPrice.payPrice !== computedPrice.originalTotalPrice"
                class="text-sm text-[#1a1a1a] ml-1"
              >
                <span>已优惠¥{{ computedPrice?.totalDiscountAmount }}</span>
              </span>
            </div>
          </div>
          <button
            class="bg-[#6D3202] h-15 w-[180px] flex items-center justify-center text-white rounded-full font-bold text-[20px] transition-all duration-200 active:scale-[0.95]"
            @click="handlePay"
          >
            去支付
          </button>
        </div>
      </div>
    </div>
    <PaymentPopup ref="paymentPopup" @close="handleClosePaymentPopup" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import PaymentPopup from './payment-popup/index.vue'
import GoodsItem from './GoodsItem.vue'
import BackWidget from '@/views/consumer/components/BackWidget.vue'
import { useCart } from '../useCart'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { FreeMode, Scrollbar, Mousewheel } from 'swiper/modules'
import type { SwiperOptions } from 'swiper/types'
import IconClearCart from '../imgs/clear-cart.png'
import { toast } from '@/libs/toast'
import { useRouter } from 'vue-router'
import { storeLocalOrderToWechat } from '@/api/consumer'

const router = useRouter()
const paymentPopup = ref()
const backWidget = ref()
const { items, total, totalCount, clearCart, computedPrice } = useCart()

const swiperOptions: SwiperOptions = {
  direction: 'vertical',
  slidesPerView: 'auto',
  freeMode: true,
  scrollbar: true,
  mousewheel: true,
}
const handlePay = async () => {
  if (!items.value.length) return
  const carInfoStr = localStorage.getItem('carInfo')
  if (!carInfoStr) {
    toast('下单失败，未获取到售卖车信息，请稍后再试')
    return
  }
  const carInfo = JSON.parse(carInfoStr)
  const { data } = await storeLocalOrderToWechat({
    orderInfo: JSON.stringify({
      items: items.value,
      orderParams: {
        carId: carInfo.id,
        carName: carInfo.carName,
        areaName: carInfo.runAreaName,
        areaId: carInfo.runAreaId,
      },
    }),
  })
  paymentPopup.value.show(data.value.data)
  backWidget.value.pause()
}

const handleClearCart = () => {
  clearCart()
  router.replace('/consumer/home')
}

const handleClosePaymentPopup = () => {
  backWidget.value.resume()
}
</script>
