<template>
  <div class="actions-container">
    <div class="action-buttons">
      <button class="action-btn start">
        <div class="btn-icon green"></div>
      </button>
      <button class="action-btn pause">
        <div class="btn-icon yellow"></div>
      </button>
      <button class="action-btn stop">
        <div class="btn-icon red"></div>
      </button>
    </div>
    <div class="navigation-controls">
      <button class="nav-btn prev">◄</button>
      <button class="nav-btn next">►</button>
      <button class="nav-btn up">▲</button>
    </div>
  </div>
</template>

<script setup>
// 执行动作逻辑
</script>

<style scoped>
.actions-container {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;
}

.btn-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.green {
  background-color: #52c41a;
}
.yellow {
  background-color: #faad14;
}
.red {
  background-color: #f5222d;
}

.navigation-controls {
  display: flex;
  gap: 5px;
}

.nav-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;
}
</style>
