import { ref, createApp, h } from 'vue'
import ToastContainer from '../components/ToastContainer.vue'

interface ToastOptions {
  message: string
  duration?: number
}

// 创建一个响应式对象来管理toast的状态
const visible = ref(false)
const message = ref('')
const duration = ref(3000)

// 创建一个div元素，用于挂载toast组件
let toastInstance: any = null

function createToastInstance() {
  // 如果实例已经存在，则不重复创建
  if (toastInstance) return

  const toastDiv = document.createElement('div')
  document.body.appendChild(toastDiv)

  // 创建toast应用
  toastInstance = createApp({
    setup() {
      const closeToast = () => {
        visible.value = false
      }

      return () =>
        h(ToastContainer, {
          message: message.value,
          duration: duration.value,
          visible: visible.value,
          onClose: closeToast,
        })
    },
  })

  // 挂载应用
  toastInstance.mount(toastDiv)
}

// 显示toast的函数
export function toast(options: ToastOptions | string) {
  // 创建Toast实例（如果尚未创建）
  createToastInstance()

  // 处理参数，可以是字符串或对象
  if (typeof options === 'string') {
    message.value = options
    duration.value = 3000
  } else {
    message.value = options.message
    duration.value = options.duration || 3000
  }

  // 显示toast
  visible.value = true
}

// 可选：提供一个关闭toast的方法
export function closeToast() {
  visible.value = false
}
