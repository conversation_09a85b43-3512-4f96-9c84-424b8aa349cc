<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>WebSocket 连接配置</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>

      <div class="modal-body">
        <!-- 警告提示 -->
        <div class="warning-notice">
          <div class="warning-icon">⚠️</div>
          <div class="warning-text">
            <strong>注意：</strong>
            非技术人员请勿随意操作！
            <br />
            不要随意修改WebSocket地址，可能会导致连接失败！
          </div>
        </div>

        <!-- 当前连接状态 -->
        <div class="connection-status">
          <label>当前连接状态：</label>
          <span class="status-badge" :class="statusClass">
            {{ statusText }}
          </span>
        </div>

        <!-- 当前地址显示 -->
        <div class="current-address">
          <label>当前WebSocket地址：</label>
          <div class="address-display">{{ currentAddress }}</div>
        </div>

        <!-- 新地址输入 -->
        <div class="input-group">
          <label for="newAddress">新的WebSocket地址：</label>
          <input
            id="newAddress"
            v-model="newAddress"
            type="text"
            placeholder="ws://localhost:8080"
            class="address-input"
            :class="{ error: inputError }"
          />
          <div v-if="inputError" class="error-message">{{ inputError }}</div>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResult" class="test-result" :class="testResult.type">
          <div class="result-icon">
            {{ testResult.type === 'success' ? '✅' : '❌' }}
          </div>
          <div class="result-text">{{ testResult.message }}</div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-test" :disabled="testing || !newAddress" @click="testConnection">
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
        <button class="btn btn-cancel" @click="closeModal">取消</button>
        <button
          class="btn btn-confirm"
          :disabled="!newAddress || inputError || !testResult?.type === 'success'"
          @click="confirmChange"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useWebSocketConfig } from '@/views/maintenance/hooks/useWebSocketConfig'
import { toast } from '@/libs/toast'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:visible', 'confirm'])

const {
  currentAddress,
  status,
  testConnection: testWsConnection,
  updateAddress,
  testing,
} = useWebSocketConfig()

const newAddress = ref('')
const inputError = ref('')
const testResult = ref(null)

// 连接状态样式
const statusClass = computed(() => {
  switch (status.value) {
    case 'OPEN':
      return 'status-connected'
    case 'CONNECTING':
      return 'status-connecting'
    case 'CLOSED':
      return 'status-disconnected'
    default:
      return 'status-unknown'
  }
})

// 连接状态文本
const statusText = computed(() => {
  switch (status.value) {
    case 'OPEN':
      return '已连接'
    case 'CONNECTING':
      return '连接中'
    case 'CLOSED':
      return '已断开'
    default:
      return '未知'
  }
})

// 监听输入变化，验证地址格式
watch(newAddress, (value) => {
  testResult.value = null
  inputError.value = ''

  if (value && !isValidWebSocketUrl(value)) {
    inputError.value = 'WebSocket地址格式不正确，应以 ws:// 或 wss:// 开头'
  }
})

// 验证WebSocket URL格式
const isValidWebSocketUrl = (url) => {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'ws:' || urlObj.protocol === 'wss:'
  } catch {
    return false
  }
}

// 测试连接
const testConnection = async () => {
  if (!newAddress.value || inputError.value) return

  try {
    const result = await testWsConnection(newAddress.value)
    testResult.value = result
    if (result.type === 'error') {
      toast('连接测试失败：' + result.message, 'error')
    } else {
      toast('连接测试成功', 'success')
    }
  } catch (error) {
    testResult.value = {
      type: 'error',
      message: '测试连接时发生错误',
    }
    toast('测试连接失败', 'error')
  }
}

// 确认修改
const confirmChange = () => {
  if (!newAddress.value || inputError.value) return

  if (testResult.value?.type !== 'success') {
    toast('请先测试连接成功后再确认修改', 'warning')
    return
  }

  updateAddress(newAddress.value)
  emit('confirm', newAddress.value)
  toast('WebSocket地址已更新', 'success')
  closeModal()
}

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
  // 重置状态
  newAddress.value = ''
  inputError.value = ''
  testResult.value = null
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  closeModal()
}

// 监听弹窗显示，初始化新地址为当前地址
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      newAddress.value = currentAddress.value
    }
  },
)
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
}

.modal-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: #f5f7fa;
  color: #606266;
}

.modal-body {
  padding: 24px;
}

.warning-notice {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text {
  color: #f56c6c;
  font-size: 14px;
  line-height: 1.5;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

.connection-status label {
  font-weight: 500;
  color: #303133;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-connected {
  background-color: #f0f9ff;
  color: #67c23a;
  border: 1px solid #b3e19d;
}

.status-connecting {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.status-disconnected {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.status-unknown {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #d3d4d6;
}

.current-address {
  margin-bottom: 20px;
}

.current-address label {
  display: block;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.address-display {
  padding: 10px 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  font-family: monospace;
  word-break: break-all;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.address-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  font-family: monospace;
  box-sizing: border-box;
}

.address-input:focus {
  outline: none;
  border-color: #409eff;
}

.address-input.error {
  border-color: #f56c6c;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 6px;
}

.test-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.test-result.success {
  background-color: #f0f9ff;
  border: 1px solid #b3e19d;
  color: #67c23a;
}

.test-result.error {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  color: #f56c6c;
}

.result-icon {
  font-size: 16px;
}

.result-text {
  font-size: 14px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-test {
  background-color: #e6a23c;
  color: white;
}

.btn-test:hover:not(:disabled) {
  background-color: #ebb563;
}

.btn-cancel {
  background-color: #909399;
  color: white;
}

.btn-cancel:hover {
  background-color: #a6a9ad;
}

.btn-confirm {
  background-color: #409eff;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #66b1ff;
}
</style>
