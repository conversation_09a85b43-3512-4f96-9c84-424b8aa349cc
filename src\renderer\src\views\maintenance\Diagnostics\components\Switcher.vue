<template>
  <div class="switcher-container">
    <span v-if="inactiveText" class="switcher-text inactive-text">{{ inactiveText }}</span>
    <div
      class="switcher"
      :class="{ 'is-checked': modelValue, 'is-disabled': disabled }"
      @click="toggleSwitch"
    >
      <div class="switcher-core">
        <div class="switcher-button"></div>
      </div>
    </div>
    <span v-if="activeText" class="switcher-text active-text" :class="{ 'is-active': modelValue }">
      {{ activeText }}
    </span>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  activeText: {
    type: String,
    default: '',
  },
  inactiveText: {
    type: String,
    default: '',
  },
  activeColor: {
    type: String,
    default: '#13ce66',
  },
  inactiveColor: {
    type: String,
    default: '#dcdfe6',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const toggleSwitch = () => {
  if (props.disabled) return

  const newValue = !props.modelValue
  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<style scoped>
.switcher-container {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  font-size: 14px;
  line-height: 20px;
}

.switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  height: 20px;
  min-width: 40px;
  cursor: pointer;
  vertical-align: middle;
}

.switcher-core {
  position: relative;
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: v-bind('modelValue ? activeColor : inactiveColor');
  transition: background-color 0.3s;
  box-sizing: border-box;
}

.switcher-button {
  position: absolute;
  top: 2px;
  left: v-bind('modelValue ? "22px" : "2px"');
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: left 0.3s;
}

.switcher.is-checked .switcher-core {
  background-color: v-bind('activeColor');
}

.switcher-text {
  margin: 0 5px;
  color: #606266;
  font-size: 14px;
}

.active-text.is-active {
  color: v-bind('activeColor');
}

.switcher:not(.is-checked) + .active-text {
  color: #606266;
}

/* 户外强光环境下增强对比度 */
@media (max-width: 1024px) {
  .switcher-core {
    width: 44px;
    height: 22px;
  }

  .switcher-button {
    width: 18px;
    height: 18px;
    left: v-bind('modelValue ? "24px" : "2px"');
  }

  .switcher-text {
    font-weight: 500;
    font-size: 15px;
  }
}

/* 禁用状态样式 */
.switcher.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.switcher.is-disabled .switcher-core {
  background-color: #f5f5f5 !important;
}

.switcher.is-disabled .switcher-button {
  background-color: #c0c4cc !important;
}

.switcher.is-disabled + .switcher-text {
  color: #c0c4cc !important;
}
</style>
