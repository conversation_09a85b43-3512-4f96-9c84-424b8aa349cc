<template>
  <div class="px-4 bg-white select-none">
    <!-- 杯型选择 -->
    <div class="mb-5 flex items-center gap-5">
      <span class="font-bold text-[20px]">杯型</span>
      <SpecificationRadio
        :options="cupTypeOptions"
        :default-value="selectedCupValue"
        @change="selectedCupValue = $event"
      />
    </div>

    <!-- 温度选择 -->
    <div class="mb-5 flex items-center gap-5">
      <span class="font-bold text-[20px]">温度</span>
      <SpecificationRadio
        :options="tempOptions"
        :default-value="selectedTempValue"
        @change="selectedTempValue = $event"
      />
    </div>

    <!-- 甜度选择 -->
    <div class="mb-5 flex items-center gap-5">
      <span class="font-bold text-[20px] whitespace-nowrap">甜度</span>
      <SpecificationRadio
        :options="sweetOptions"
        :default-value="selectedSweetValue"
        @change="selectedSweetValue = $event"
      />
    </div>
    <!-- 数量 -->
    <div class="mb-5 flex items-center gap-5">
      <span class="font-bold text-[20px] whitespace-nowrap">数量</span>
      <div class="flex items-center justify-space">
        <div
          class="w-[40px] h-[40px] border-[#D2C0B1] text-[#6D3202] border-1 rounded-full flex items-center justify-center text-2xl cursor-pointer transition-all duration-200 active:scale-[0.95]"
          @click.stop="decreaseQuantity"
        >
          <div class="w-[20px] h-[2px] bg-[#D2C0B1]"></div>
        </div>
        <div class="w-[52px] text-center mr-1 text-gray-900 text-[20px]">
          {{ quantity }}
        </div>
        <div
          class="w-[40px] h-[40px] bg-[#6D3202] rounded-full flex items-center justify-center text-white text-4xl cursor-pointer font-thin transition-all duration-200 active:scale-[0.95]"
          @click.stop="increaseQuantity"
        >
          +
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { GoodsDetails } from '../../types'
import { ref, watch, computed } from 'vue'
import SpecificationRadio from './SpecificationRadio.vue'

const props = defineProps<{
  data: GoodsDetails
  cupPriceList: GoodsDetails['cupPriceList']
  sweetList: GoodsDetails['sweetList']
  selectedCup: string
  selectedTemp: string
  selectedSweet: string
  fullSpecNames: string[]
  isTemperatureDisabled: (tempCode: string) => boolean
  isCupTypeDisabled: (cupCode: string) => boolean
  availableTemperatures: { code: string; name: string }[]
  availableCupTypes: { code: string; name: string }[]
}>()

const emit = defineEmits<{
  (e: 'update:selectedCup', value: string): void
  (e: 'update:selectedTemp', value: string): void
  (e: 'update:selectedSweet', value: string): void
  (e: 'update:fullSpecNames', value: string[]): void
  (e: 'update:quantity', value: number): void
}>()

// 杯型选择
const cupTypeOptions = computed(() => {
  return props.availableCupTypes?.map((cup) => ({
    label: cup.name,
    value: cup.code,
    disabled: props.isCupTypeDisabled(cup.code),
  }))
})
// 温度选择
const tempOptions = computed(() => {
  return props.availableTemperatures?.map((temp) => ({
    label: temp.name,
    value: temp.code,
    disabled: props.isTemperatureDisabled(temp.code),
  }))
})
// 甜度选择
const sweetOptions = computed(() => {
  return props.sweetList?.map((sweet) => ({
    label: sweet.formulaName,
    value: sweet.formulaCode,
    disabled: false,
  }))
})

const quantity = ref(1)

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
    emit('update:quantity', quantity.value)
  }
}
const increaseQuantity = () => {
  quantity.value++
  emit('update:quantity', quantity.value)
}

// 保持与父组件的双向绑定
const selectedCupValue = ref(props.selectedCup)
const selectedTempValue = ref(props.selectedTemp)
const selectedSweetValue = ref(props.selectedSweet)

// 监听内部状态变化，并向父组件提交更新
watch(selectedCupValue, (newValue) => {
  emit('update:selectedCup', newValue)
})

watch(selectedTempValue, (newValue) => {
  emit('update:selectedTemp', newValue)
})

watch(selectedSweetValue, (newValue) => {
  emit('update:selectedSweet', newValue)
})

// 监听父组件传递下来的props变化，更新内部状态
watch(
  () => props.selectedCup,
  (newValue) => {
    if (selectedCupValue.value !== newValue) {
      selectedCupValue.value = newValue
    }
  },
)

watch(
  () => props.selectedTemp,
  (newValue) => {
    if (selectedTempValue.value !== newValue) {
      selectedTempValue.value = newValue
    }
  },
)

watch(
  () => props.selectedSweet,
  (newValue) => {
    if (selectedSweetValue.value !== newValue) {
      selectedSweetValue.value = newValue
    }
  },
)
</script>
