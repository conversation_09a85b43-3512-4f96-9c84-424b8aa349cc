<template>
  <div class="p-4 w-[840px] m-auto">
    <swiper
      :slides-per-view="5"
      :space-between="10"
      :modules="modules"
      navigation
      class="w-full"
      :class="data.length < 5 && 'flex justify-center not-enough-orders'"
    >
      <swiper-slide v-for="order in data" :key="order.id">
        <OrderItem :order="order" />
      </swiper-slide>
    </swiper>

    <div v-if="data.length === 0" class="text-center py-8 text-gray-500">暂无订单</div>
  </div>
</template>

<script setup lang="ts">
import OrderItem from './OrderItem.vue'
// 导入 Swiper 相关组件和模块
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Navigation } from 'swiper/modules'

export type OrderItemType = {
  id: string
  orderStatus: number
  pickupPort: string
  goodsUrl: string
  goodsName: string
}

withDefaults(
  defineProps<{
    data: OrderItemType[]
  }>(),
  {
    data: () => [
      /* {
        id: '1',
        orderStatus: 2,
        pickupPort: '01',
        goodsUrl: '/path-to-images/bubble-tea-1.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      },
      {
        id: '2',
        orderStatus: 3,
        pickupPort: '02',
        goodsUrl: '/path-to-images/bubble-tea-2.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      },
      {
        id: '3',
        orderStatus: 3,
        pickupPort: '01',
        goodsUrl: '/path-to-images/bubble-tea-1.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      },
      {
        id: '4',
        orderStatus: 3,
        pickupPort: '01',
        goodsUrl: '/path-to-images/bubble-tea-1.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      },
      {
        id: '5',
        orderStatus: 3,
        pickupPort: '01',
        goodsUrl: '/path-to-images/bubble-tea-1.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      },
      {
        id: '6',
        orderStatus: 3,
        pickupPort: '01',
        goodsUrl: '/path-to-images/bubble-tea-1.jpg', // 需要替换为实际路径
        goodsName: '大师生椰拿铁',
        pellets: '标准杯，冰，全糖',
      }, */
    ],
  },
)

// Swiper 模块
const modules = [Navigation]

</script>

<style scoped>
.not-enough-orders :deep(.swiper-wrapper) {
  width: max-content;
  margin: auto;
}
</style>
