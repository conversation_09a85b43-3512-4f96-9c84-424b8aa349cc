import { createFetch } from '@vueuse/core'
import qs from 'qs'

const useFetch = createFetch({
  baseUrl: import.meta.env.VITE_API_URL,
  options: {
    async beforeFetch({ options }) {
      // 默认使用 application/application/json
      options.headers = options.headers || {}

      // 如果是 application/x-www-form-urlencoded 格式，且 body 是字符串，则尝试转换
      if (
        options.headers['Content-Type'] === 'application/x-www-form-urlencoded' &&
        options.body &&
        typeof options.body === 'string'
      ) {
        try {
          const parsedBody = JSON.parse(options.body)
          options.body = qs.stringify(parsedBody)
        } catch (error) {
          console.error('解析 body 时出错:', error)
        }
      }

      return { options }
    },
  },
  fetchOptions: { mode: 'cors' },
})
// 根据商品id和机器id查询商品详情
export const fetchMenuDetailById = (params: Record<string, string>) => {
  return useFetch('/product-server/product/productGoods/getProductGoodsDtoByMap', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 根据机器id获取商品菜单信息
export const fetchMenuListByCarId = (params: Record<string, string>) => {
  return useFetch('/product-server/product/menuManage/getMenuListByCarId', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 存储本地订单转移到微信小程序支付
export const storeLocalOrderToWechat = (params: Record<string, string>) => {
  return useFetch('/product-server/product/productOrder/localSkipSave').post(params).json()
}

// 查询支付结果
export const queryPaymentResult = (sign: string) => {
  return useFetch(`/product-server/product/productOrder/queryOrderDetailBySign?sign=${sign}`)
    .get()
    .json()
}

// 删除缓存订单
export const deleteCacheOrder = (sign: string) => {
  return useFetch(`/product-server/product/productOrder/localSkipDel?sign=${sign}`).get().json()
}

// 查询订单详情 和支付结果同一个接口
export const queryOrderDetail = (sign: string) => {
  return useFetch(`/product-server/product/productOrder/queryOrderDetailBySign?sign=${sign}`)
    .get()
    .json()
}

// 立即开柜
export const openBoxDoor = (params: { orderId: string }) => {
  return useFetch('/wcs-server/wcs/deviceSellCar/openBoxDoor', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 获取车辆名称接口
export const getCarName = (params: { carId: string }) => {
  return useFetch('/wcs-server/wcs/deviceSellCar/queryCarInfoByCarId', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 查询广告
export const fetchQueryAdvertises = (params: {
  scopId: string
  scopType: string
  taskMaterialType: string
}) => {
  return useFetch(`/dispatch-server/dispatch/adTaskManage/getAdTaskMaterialDtoByMap`, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 轮询接口用于判断用户是否活跃，控制车辆是否可以运行
export const refreshCarWaitTime = (params: Record<string, string | number>) => {
  return useFetch('/wcs-server/wcs/deviceSellCar/refreshCarWaitTime', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
    .post(params)
    .json()
}

// 消费者-动态计算购物车商品价格
export const calculateLowestPrice = (query: Record<string, string | number | any>) => {
  return useFetch('/product-server/product/productPromotionalActivities/calculateLowestPrice')
    .post(query)
    .json()
}
