import { ElectronAPI } from '@electron-toolkit/preload'

interface ApiMethods {
  // 虚拟键盘相关
  openVirtualKeyboard: () => void
  closeVirtualKeyboard: () => void
  // 窗口相关
  getWindowId: () => Promise<number>
  enterKiosk: () => Promise<boolean>
  exitKiosk: () => Promise<boolean>
  // 自启动相关
  setAutoLaunch: (enable: boolean) => Promise<any>
  getAutoLaunch: () => Promise<any>
  // 网络相关
  checkLatency: (url: string) => Promise<{ time?: number; alive?: boolean; error?: string }>
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: ApiMethods
    electronAPI: {
      createWindow: () => Promise<void>
      toggleKiosk: (windowId?: number) => Promise<void>
      tileWindows: () => Promise<void>
    }
  }
}
