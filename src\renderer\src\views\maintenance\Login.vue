<template>
  <div class="min-h-screen bg-gray-50 w-screen h-screen flex flex-col">
    <!-- Header with return link -->
    <div class="px-6 py-4 bg-white shadow-sm w-max rounded-lg fixed top-4 left-4 shadow-lg">
      <a
        href="#"
        class="text-sm flex items-center text-blue-500 transition-colors w-max"
        @click="router.back()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        返回用户端
      </a>
    </div>

    <!-- Main content -->
    <div class="flex flex-col items-center justify-center flex-1 px-4">
      <div class="w-full max-w-md">
        <!-- Title and subtitle -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">运维管理后台</h1>
          <p class="text-gray-500">安全登录，管理您的系统</p>
        </div>

        <!-- Login Card -->
        <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-100">
          <div class="mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-2">验证登录</h2>
            <p class="text-gray-500 text-sm">请输入6位登录验证码</p>
          </div>

          <!-- Error message -->
          <div v-if="errorMessage" class="mb-4 text-center">
            <p class="text-red-500">{{ errorMessage }}</p>
            <p v-if="remainingAttempts > 0" class="text-sm text-gray-500 mt-1">
              还剩 {{ remainingAttempts }} 次尝试机会
            </p>
          </div>

          <!-- Verification code input -->
          <div class="flex justify-between mb-8">
            <input
              v-for="(digit, index) in 6"
              :key="index"
              ref="inputs"
              v-model="verificationCode[index]"
              type="text"
              maxlength="1"
              readonly
              class="w-12 h-12 border border-gray-300 rounded-md text-center text-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
              :class="{ 'border-red-500': errorMessage }"
            />
          </div>

          <!-- Number Keyboard -->
          <NumberKeyboard
            v-model:value="verificationCode"
            @confirm="submitCode"
            @clear="clearInputs"
          />
        </div>

        <!-- Additional information or help text -->
        <div class="mt-6 text-center">
          <p class="text-sm text-gray-500">
            遇到问题？请联系
            <a href="#" class="text-blue-500 hover:underline">系统管理员</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useCountdown } from '@/hooks/useCountdown'
import NumberKeyboard from './components/NumberKeyboard.vue'
import { verifyCode, generateCode } from '@/libs/crypto'

const router = useRouter()
useCountdown()

const verificationCode = ref(['', '', '', '', '', ''])
const inputs = ref<HTMLInputElement[]>([])
const errorMessage = ref('')
const attemptCount = ref(0)
const maxAttempts = 3
const remainingAttempts = ref(maxAttempts)

const carId = localStorage.getItem('carId')
generateCode(carId, new Date()).then((code) => {
  verificationCode.value = code.split('')
})

// Submit verification code
const submitCode = async () => {
  const code = verificationCode.value.join('')
  if (code.length === 6) {
    const verified = await verifyCode(carId, new Date(), code)
    if (verified) {
      // 验证成功，清除错误信息
      errorMessage.value = ''
      router.push('/maintenance/diagnostics')
    } else {
      // 验证失败，增加尝试次数并显示错误信息
      attemptCount.value++
      remainingAttempts.value = maxAttempts - attemptCount.value

      if (remainingAttempts.value <= 0) {
        errorMessage.value = '已达到最大尝试次数，请稍后再试'
        // 这里可以添加锁定逻辑，例如禁用输入
      } else {
        errorMessage.value = '验证码错误，请重新输入'
      }

      // 清空输入以便用户重新输入
      clearInputs()
    }
  }
}

// Clear all inputs
const clearInputs = () => {
  verificationCode.value = ['', '', '', '', '', '']
}
</script>
