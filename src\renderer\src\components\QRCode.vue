<template>
  <div class="qrcode-container">
    <canvas ref="qrCodeRef" class="qrcode-wrapper"></canvas>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, PropType } from 'vue'
import * as QRCodeGenerator from 'qrcode'
const props = defineProps({
  value: {
    type: String,
    required: true,
    default: '',
  },
  size: {
    type: Number,
    default: 200,
  },
  color: {
    type: String,
    default: '#000',
  },
  bgColor: {
    type: String,
    default: '#FFF',
  },
  errorCorrectionLevel: {
    type: String as PropType<'L' | 'M' | 'Q' | 'H'>,
    default: 'H',
    validator: (value: string) => ['L', 'M', 'Q', 'H'].includes(value),
  },
  margin: {
    type: Number,
    default: 0,
  },
})
const qrCodeRef = ref<HTMLCanvasElement | null>(null)

const generateQRCode = () => {
  if (!qrCodeRef.value || !props.value) return

  const options = {
    width: props.size,
    margin: props.margin,
    color: {
      dark: props.color,
      light: props.bgColor,
    },
    errorCorrectionLevel: props.errorCorrectionLevel,
  }

  QRCodeGenerator.toCanvas(qrCodeRef.value, props.value, options, (error) => {
    if (error) {
      console.error('生成二维码出错:', error)
    }
  })
}

onMounted(() => {
  generateQRCode()
})

watch(
  () => [
    props.value,
    props.size,
    props.color,
    props.bgColor,
    props.errorCorrectionLevel,
    props.margin,
  ],
  () => {
    generateQRCode()
  },
)
</script>

<style scoped>
.qrcode-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qrcode-wrapper {
  display: block;
}
</style>
