<template>
  <div
    class="w-[160px] h-[274px] rounded-lg bg-white overflow-hidden border border-[#e9e7e5] border-solid"
  >
    <!-- 订单状态和取货口 -->
    <div class="flex flex-col justify-between items-center p-3 py-2">
      <span :class="getStatusColorClass(order.orderStatus)">
        {{ getStatusText(order.orderStatus) }}
      </span>
      <span
        class="text-gray-800"
        :class="order.orderStatus.toString() !== SingleOrderStatus.FINISHED && 'opacity-0'"
      >
        取货口: {{ order.pickupPort }}
      </span>
    </div>

    <!-- 饮品图片 -->
    <div class="pb-2">
      <GoodsImage
        class="m-auto"
        :src="order.goodsUrl"
        alt="饮品图片"
        width="140px"
        height="140px"
      />
    </div>

    <!-- 饮品名称 -->
    <div class="px-3 pb-1">
      <h3 class="text text-center font-bold text-gray-900">{{ order.goodsName }}</h3>
    </div>

    <!-- 饮品规格 -->
    <div class="px-3 pb-3">
      <p class="text-sm text-gray-500 text-center">
        {{ order.pellets }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import GoodsImage from '../../components/GoodsImage.vue'
import { SingleOrderStatus } from '@/types/consumer'
// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0:
      return '待制作'
    case 1:
      return '制作中'
    case 2:
      return '待取货'
    case 3:
      return '已取货'
    case 4:
      return '已取消'
    default:
      return '未知状态'
  }
}

// 获取状态颜色类
const getStatusColorClass = (status) => {
  switch (status) {
    case 0:
      return 'text-[#356AFD]'
    case 1:
      return 'text-[#6D3202]'
    case 2:
      return 'text-[#00C290]'
    case 3:
      return 'text-[#656666]'
    case 4:
      return 'text-[#656666]'
    default:
      return 'text-gray-500'
  }
}

defineProps({
  order: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      orderStatus: 0, // 0待制作 1制作中 2待取货 3已取货 4已取消
      pickupPort: '',
      goodsUrl: '',
      goodsName: '',
      pellets: '',
    }),
  },
})
</script>
