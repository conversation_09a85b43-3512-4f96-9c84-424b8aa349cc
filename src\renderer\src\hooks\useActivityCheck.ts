// 检查用户是否离线, 离线则重置页面
import { useCart } from '@/views/consumer/goods-menu/useCart'
import { onUnmounted, ref, computed, onMounted } from 'vue'
import { refreshCarWaitTime } from '@/api/consumer'

export const useActivityCheck = () => {
  // 检查间隔时间
  const CHECK_DURATION = 1000 * 3
  // 超时认为离线
  const OFFLINE_DURATION = 1000 * 60 * 3

  const lastActivityTime = ref(0)
  // 扫码参数
  const scanParams = ref<Record<string, string>>({})

  const { clearCart } = useCart()
  const activityStatus = ref(false)
  let timer: NodeJS.Timeout | null = null

  const countdown = computed(() => {
    return ~~Math.max(0, OFFLINE_DURATION - (Date.now() - lastActivityTime.value)) / 1000
  })

  const resetStatus = () => {
    clearCart()
    timer && clearInterval(timer)
  }

  function handleEvent() {
    console.log('handleEvent')
    activityStatus.value = true
  }

  onMounted(() => {
    ;['click', 'touchstart', 'touchmove', 'keydown', 'scroll'].forEach((event) => {
      document.querySelector('.consumer-layout')?.addEventListener(event, handleEvent)
    })

    timer = setInterval(() => {
      if (activityStatus.value) {
        activityStatus.value = false
        lastActivityTime.value = Date.now()
        refreshCarWaitTime({ ...scanParams.value, isLocal: 1 }).then((res) => {
          if (!res.data) resetStatus()
        })
      } else {
        if (Date.now() - lastActivityTime.value > OFFLINE_DURATION) {
          resetStatus()
        }
      }
    }, CHECK_DURATION)
  })
  onUnmounted(() => {
    timer && clearInterval(timer)
    ;['click', 'touchstart', 'touchmove', 'keydown', 'scroll'].forEach((event) => {
      document.querySelector('.consumer-layout')?.removeEventListener(event, handleEvent)
    })
  })

  return {
    countdown,
    updateParams: (params: Record<string, string>) => {
      scanParams.value = params
    },
  }
}
