<template>
  <div class="websocket-log">
    <div class="log-header">
      <div class="title">
        <i class="ri-file-list-line"></i>
        <span>{{ title }}</span>
      </div>
      <div class="actions">
        <button class="clear-btn" @click="clearLogs">
          <i class="ri-delete-bin-line"></i>
          清除
        </button>
        <button class="export-btn" @click="exportLogs">
          <i class="ri-download-line"></i>
          导出
        </button>
      </div>
    </div>

    <div class="log-filter">
      <div class="search-bar">
        <i class="ri-search-line"></i>
        <input v-model="searchText" type="text" placeholder="搜索日志" @input="filterLogs" />
      </div>
      <div class="filter-options">
        <button
          v-for="type in logTypes"
          :key="type.value"
          :class="['filter-btn', { active: activeFilters.includes(type.value) }]"
          @click="toggleFilter(type.value)"
        >
          {{ type.label }}
        </button>
      </div>
    </div>

    <div ref="logContainer" class="log-content">
      <div
        v-for="(log, index) in filteredLogs"
        :key="index"
        :class="['log-item', log.type.toLowerCase()]"
      >
        <div class="log-time">{{ formatTime(log.timestamp) }}</div>
        <div class="log-type">{{ log.type }}</div>
        <div class="log-message">{{ log.message }}</div>
      </div>

      <div v-if="filteredLogs.length === 0" class="empty-log">
        <i class="ri-inbox-line"></i>
        <span>暂无日志记录</span>
      </div>
    </div>

    <div class="log-status">
      <span>总计: {{ logs.length }} 条日志</span>
      <span>已显示: {{ filteredLogs.length }} 条</span>
      <label class="auto-scroll">
        <input v-model="autoScroll" type="checkbox" />
        自动滚动
      </label>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebsocketLog',
  props: {
    title: {
      type: String,
      default: 'WebSocket 日志',
    },
    maxLogs: {
      type: Number,
      default: 1000,
    },
  },
  data() {
    return {
      logs: [
        {
          type: 'INFO',
          message: '测试日志',
          timestamp: Date.now(),
        },
        {
          type: 'INFO',
          message: '测试日志',
          timestamp: Date.now(),
        },
      ],
      searchText: '',
      autoScroll: true,
      activeFilters: ['INFO', 'WARN', 'ERR'],
      logTypes: [
        { label: '信息', value: 'INFO' },
        { label: '警告', value: 'WARN' },
        { label: '错误', value: 'ERR' },
      ],
      filteredLogs: [],
    }
  },
  watch: {
    logs() {
      this.filterLogs()
      if (this.autoScroll) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
  },
  methods: {
    // 添加新的日志
    addLog(type, message) {
      const timestamp = Date.now()
      this.logs.push({ type, message, timestamp })

      // 限制日志数量，防止内存溢出
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs)
      }
    },

    // 过滤日志
    filterLogs() {
      this.filteredLogs = this.logs.filter((log) => {
        const matchesSearch =
          !this.searchText || log.message.toLowerCase().includes(this.searchText.toLowerCase())
        const matchesType = this.activeFilters.includes(log.type)
        return matchesSearch && matchesType
      })
    },

    // 切换过滤器
    toggleFilter(type) {
      if (this.activeFilters.includes(type)) {
        if (this.activeFilters.length > 1) {
          // 至少保留一个过滤条件
          this.activeFilters = this.activeFilters.filter((t) => t !== type)
        }
      } else {
        this.activeFilters.push(type)
      }
      this.filterLogs()
    },

    // 清除所有日志
    clearLogs() {
      this.logs = []
    },

    // 导出日志
    exportLogs() {
      const content = this.logs
        .map((log) => `[${new Date(log.timestamp).toISOString()}] [${log.type}] ${log.message}`)
        .join('\n')

      const blob = new Blob([content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')

      a.href = url
      a.download = `websocket-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
      a.click()

      URL.revokeObjectURL(url)
    },

    // 格式化时间戳
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    },

    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.logContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
  },
}
</script>

<style scoped>
.websocket-log {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9f9fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Open Sans', 'Helvetica Neue', sans-serif;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e8e8e8;
}

.title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.title i {
  margin-right: 8px;
  color: #666;
}

.actions {
  display: flex;
  gap: 8px;
}

.actions button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.actions button i {
  margin-right: 4px;
}

.clear-btn {
  background-color: #f0f0f0;
  color: #666;
}

.clear-btn:hover {
  background-color: #e0e0e0;
}

.export-btn {
  background-color: #f0f0f0;
  color: #666;
}

.export-btn:hover {
  background-color: #e0e0e0;
}

.log-filter {
  display: flex;
  padding: 12px 16px;
  gap: 12px;
  background-color: white;
  border-bottom: 1px solid #e8e8e8;
  align-items: center;
}

.search-bar {
  display: flex;
  align-items: center;
  flex: 1;
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 0 12px;
  height: 36px;
}

.search-bar i {
  color: #999;
  margin-right: 8px;
}

.search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
}

.filter-options {
  display: flex;
  gap: 8px;
}

.filter-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.filter-btn.active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px 16px;
}

.log-item {
  display: flex;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: white;
  border-left: 3px solid transparent;
}

.log-item.info {
  border-left-color: #52c41a;
}

.log-item.warn {
  border-left-color: #faad14;
  background-color: #fffbe6;
}

.log-item.err {
  border-left-color: #f5222d;
  background-color: #fff1f0;
}

.log-time {
  width: 80px;
  color: #999;
  font-size: 13px;
}

.log-type {
  width: 50px;
  font-weight: 500;
  font-size: 13px;
}

.log-item.info .log-type {
  color: #52c41a;
}

.log-item.warn .log-type {
  color: #faad14;
}

.log-item.err .log-type {
  color: #f5222d;
}

.log-message {
  flex: 1;
  word-break: break-all;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
}

.empty-log {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-log i {
  font-size: 24px;
  margin-bottom: 8px;
}

.log-status {
  display: flex;
  padding: 10px 16px;
  background-color: white;
  border-top: 1px solid #e8e8e8;
  color: #666;
  font-size: 13px;
  justify-content: space-between;
}

.auto-scroll {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.auto-scroll input {
  cursor: pointer;
}
</style>
