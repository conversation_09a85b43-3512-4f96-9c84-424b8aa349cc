import { useIntervalFn } from '@vueuse/core'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export const useCountdown = (
  time: number = 120,
  callback?: () => void,
  backHome: boolean = true,
) => {
  const timeRef = ref(time)
  const router = useRouter()
  const { pause, resume } = useIntervalFn(() => {
    timeRef.value--
    if (timeRef.value <= 0) {
      callback?.()
      if (backHome) {
        router.replace('/consumer/home')
      }
    }
  }, 1000)

  const reset = () => {
    timeRef.value = time
  }

  return { time: timeRef, pause, resume, reset }
}
