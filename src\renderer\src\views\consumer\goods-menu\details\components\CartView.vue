<template>
  <div class="flex flex-col bg-white p-4 pt-0 gap-4 border-t border-[#eff1f5] border-t-solid">
    <div class="mt-2 flex justify-between items-center">
      <div class="flex items-center gap-2">
        <div class="text-base font-semibold text-[#6D3202]">
          <span class="text-sm mr-.5">¥</span>
          <span class="text-lg">{{ price }}</span>
        </div>
        <slot name="left" />
      </div>

      <div class="flex items-center justify-space">
        <div
          class="w-6 h-6 flex items-center justify-center rounded-full"
          @click.stop="decreaseQuantity"
        >
          <wd-icon name="minus-circle" size="24px" class="text-[#6D3202]" />
        </div>
        <div class="w-8 text-center mr-1 text-[#656666]">{{ quantity }}</div>
        <div
          class="w-6 h-6 flex items-center justify-center bg-[#6D3202] rounded-full"
          @click.stop="increaseQuantity"
        >
          <wd-icon name="add" size="14px" class="text-white" />
        </div>
      </div>
    </div>
    <div class="flex justify-between gap-1">
      <wd-button
        plain
        custom-class="border-[#6D3202]! text-[#6D3202]! w-full"
        size="large"
        @click="handleAddToCart"
      >
        加入购物车
      </wd-button>
      <wd-button custom-class="bg-[#6D3202]! w-full" size="large" @click="handlePay">
        立即下单
      </wd-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useCart } from '../../useCart'
import type { GoodsDetails } from '../../types'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const { addToCart } = useCart()

const props = defineProps<{
  price: number
  goods: GoodsDetails
  specification: { temp: string; sweet: string; cup: string }
}>()
const quantity = ref(1)
const router = useRouter()
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}
const increaseQuantity = () => {
  quantity.value++
}

const handleAddToCart = () => {
  addToCart({
    ...props.goods,
    price: props.price,
    $quantity: quantity.value,
    $specification: props.specification,
  })

  router.back()
}
const handlePay = () => {
  addToCart({
    ...props.goods,
    price: props.price,
    $quantity: quantity.value,
    $specification: props.specification,
  })
  router.push('/goods-menu/payments/index')
}
</script>
