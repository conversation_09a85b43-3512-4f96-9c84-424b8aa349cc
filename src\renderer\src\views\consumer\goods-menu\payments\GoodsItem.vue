<template>
  <div class="flex items-center w-[600px]">
    <GoodsImage :src="goods.imagePath" width="160px" height="160px" />
    <div class="flex-1 ml-5 flex flex-col justify-between h-[160px]">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <span class="block text-[20px] font-bold text-gray-900">
            {{ goods.productName }}
          </span>
          <span class="block text-lg text-gray-600 mt-2">
            {{ (goods as CartItem).$fullSpecNames.join(',') }}
          </span>
        </div>
      </div>
      <div
        v-if="(goods as CartItem).promotionLabel"
        class="w-fit bg-white flex items-center justify-center px-1 rounded border border-[#6D320233] mt-auto"
      >
        <span class="text-[#6D3202] text-sm">
          {{ (goods as CartItem).promotionLabel }}
        </span>
      </div>
      <div class="flex justify-between items-center text-[#6D3202]">
        <span class="text-base font-semibold">
          <span class="text-2xl font-semibold">
            <span class="text-lg mr-1">¥</span>
            <span>{{ (goods as CartItem).discountPrice }}</span>
          </span>
          <span
            v-if="promotion && promotion?.promotionalPrice !== goods.price"
            class="text-sm line-through text-gray-600 ml-1"
          >
            ¥{{ goods.price }}
          </span>
        </span>
        <div class="flex items-center justify-space">
          <div
            class="w-[40px] h-[40px] border-[#D2C0B1] text-[#6D3202] border-1 rounded-full flex items-center justify-center text-2xl cursor-pointer"
            @click.stop="decreaseFromCart(goods as CartItem, beforeDecrease)"
          >
            <div class="w-[20px] h-[2px] bg-[#D2C0B1]"></div>
          </div>
          <div class="w-[52px] text-center mr-1 text-gray-900 text-[20px]">
            {{ (goods as CartItem).$quantity }}
          </div>
          <div
            class="w-[40px] h-[40px] bg-[#6D3202] rounded-full flex items-center justify-center text-white text-4xl cursor-pointer font-thin"
            @click.stop="addToCart(goods as CartItem)"
          >
            +
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 商品 item 菜单 购物车 订单详情使用
import { GoodsDetails, CartItem } from '@/views/consumer/goods-menu/types'
import GoodsImage from '@/views/consumer/components/GoodsImage.vue'
import { useCart } from '@/views/consumer/goods-menu/useCart'
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const props = defineProps<{ goods: GoodsDetails; referrer: 'cart' }>()
const { addToCart, decreaseFromCart, items } = useCart()
const router = useRouter()

// 计算商品促销价
const promotion = computed(() => {
  const promotionalList = props.goods?.promotionalList?.filter(
    (item) =>
      (item.promotionType === '1' && item.goodsId === props.goods.id) || item.promotionType !== '1',
  )
  const promotionalPriceList = promotionalList.map((item) => item.promotionalPrice)
  if (promotionalPriceList.length > 0) {
    return promotionalList[promotionalPriceList.indexOf(Math.min(...promotionalPriceList))]
  }
  return null
})

const beforeDecrease = () => {
  const timer = setTimeout(() => {
    // 购物车为空就返回首页
    if (!items.value.length) router.replace('/consumer/home')
    clearTimeout(timer)
  }, 500)
  return Promise.resolve(true)
}
</script>
