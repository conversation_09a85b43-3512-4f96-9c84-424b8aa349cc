import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false)
  const authError = ref('')

  // 验证码验证函数
  function validateCode(code: string) {
    // 在实际项目中，可以调用API验证，这里简化为本地验证
    // 假设有效的维护验证码是 '123456'
    if (code === '123456') {
      isAuthenticated.value = true
      authError.value = ''
      return true
    } else {
      authError.value = '验证码错误，请重试'
      return false
    }
  }

  function logout() {
    isAuthenticated.value = false
  }

  return {
    isAuthenticated,
    authError,
    validateCode,
    logout,
  }
})
