[{"groupName": "全流程测试", "controls": [{"id": 1, "label": "落杯测试", "startCommandLabel": "落杯启动", "commandKey": "luobei", "flowNumber": "1", "parameters": [{"key": "deviceParams", "label": "落杯数量", "type": "number_input", "defaultValue": "1"}]}, {"id": 2, "label": "出冰测试", "startCommandLabel": "出冰启动", "commandKey": "chubing", "flowNumber": "2", "parameters": [{"key": "deviceParams", "label": "出冰时间(5s=50g, 10s=100g, 15s=150g)", "type": "number_input", "defaultValue": "5"}]}, {"id": 3, "label": "X轴移动测试", "startCommandLabel": "移动启动", "commandKey": "yidong", "flowNumber": "3", "parameters": [{"key": "deviceParams", "label": "移动位置点", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 初始位"}, {"value": "1", "label": "1: 落杯位"}, {"value": "2", "label": "2: 落冰位"}, {"value": "3", "label": "3: 落料位"}]}]}, {"id": 4, "label": "磨豆萃取测试", "startCommandLabel": "萃取启动", "commandKey": "mod<PERSON><PERSON><PERSON><PERSON>", "flowNumber": "4", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "3"}, {"key": "deviceParams1", "label": "磨豆量", "type": "number_input", "defaultValue": "15"}, {"key": "deviceParams2", "label": "萃取温度", "type": "number_input", "defaultValue": "85"}, {"key": "deviceParams3", "label": "萃取时间", "type": "number_input", "defaultValue": "10"}]}, {"id": 5, "label": "制奶测试", "startCommandLabel": "制奶启动", "commandKey": "<PERSON><PERSON>ai", "flowNumber": "5", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "4"}, {"key": "deviceParams1", "label": "通道号", "type": "select", "defaultValue": "1", "options": [{"value": "1", "label": "1: 粉仓1"}, {"value": "2", "label": "2: 粉仓2"}, {"value": "3", "label": "3: 粉仓3"}, {"value": "4", "label": "4: 粉仓4"}, {"value": "5", "label": "5: 粉仓5"}, {"value": "6", "label": "6: 粉仓6"}, {"value": "7", "label": "7: 果酱仓1"}, {"value": "8", "label": "8: 果酱仓2"}, {"value": "9", "label": "9: 果酱仓3"}, {"value": "10", "label": "10: 果酱仓4"}]}, {"key": "deviceParams2", "label": "注水量", "type": "number_input", "defaultValue": "100"}, {"key": "deviceParams3", "label": "水温状态", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 冷水"}, {"value": "1", "label": "1: 热水"}]}, {"key": "deviceParams4", "label": "原料用量", "type": "number_input", "defaultValue": "50"}]}, {"id": 6, "label": "Y轴移动测试", "startCommandLabel": "移动启动", "commandKey": "yidong_y", "flowNumber": "6", "parameters": [{"key": "deviceParams", "label": "移动位置点", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 初始位"}, {"value": "1", "label": "1: 落料位"}, {"value": "2", "label": "2: 封口位"}, {"value": "3", "label": "3: 喷码位"}, {"value": "4", "label": "4: 落盖位"}, {"value": "5", "label": "5: 压盖位"}, {"value": "6", "label": "6: 暂存位"}]}]}, {"id": 7, "label": "注液器测试", "startCommandLabel": "注液器启动", "commandKey": "<PERSON><PERSON><PERSON><PERSON>", "flowNumber": "7", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "3"}, {"key": "deviceParams1", "label": "液体进料等待时间", "type": "number_input", "defaultValue": "8"}, {"key": "deviceParams2", "label": "搅拌时间", "type": "number_input", "defaultValue": "3"}, {"key": "deviceParams3", "label": "开门时间", "type": "number_input", "defaultValue": "3"}]}, {"id": 8, "label": "封口测试", "startCommandLabel": "封口启动", "commandKey": "feng<PERSON>u", "flowNumber": "8", "parameters": [{"key": "deviceParams", "label": "封口时间", "type": "number_input", "defaultValue": "18"}]}, {"id": 9, "label": "喷码测试", "startCommandLabel": "喷码启动", "commandKey": "penma", "flowNumber": "9", "parameters": [{"key": "deviceParams", "label": "喷码状态", "type": "select", "defaultValue": "finish", "options": [{"value": "finish", "label": "finish: 喷码完成"}, {"value": "error", "label": "error: 出错(缺墨断连等)"}]}]}, {"id": 10, "label": "落盖测试", "startCommandLabel": "落盖启动", "commandKey": "luogai", "flowNumber": "10", "parameters": [{"key": "deviceParams", "label": "落盖时间", "type": "number_input", "defaultValue": "3"}]}, {"id": 11, "label": "压盖测试", "startCommandLabel": "压盖启动", "commandKey": "yagai", "flowNumber": "11", "parameters": [{"key": "deviceParams", "label": "压盖时间", "type": "number_input", "defaultValue": "3"}]}, {"id": 12, "label": "移位测试", "startCommandLabel": "移位启动", "commandKey": "yiwei", "flowNumber": "12", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "当前位", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 暂存位"}, {"value": "1", "label": "1: 1号预存位"}, {"value": "2", "label": "2: 2号预存位"}, {"value": "3", "label": "3: 3号预存位"}, {"value": "4", "label": "4: 4号预存位"}, {"value": "5", "label": "5: 5号预存位"}, {"value": "6", "label": "6: 6号预存位"}, {"value": "7", "label": "7: 废弃位"}]}, {"key": "deviceParams2", "label": "目标位", "type": "select", "defaultValue": "1", "options": [{"value": "0", "label": "0: 暂存位"}, {"value": "1", "label": "1: 1号预存位"}, {"value": "2", "label": "2: 2号预存位"}, {"value": "3", "label": "3: 3号预存位"}, {"value": "4", "label": "4: 4号预存位"}, {"value": "5", "label": "5: 5号预存位"}, {"value": "6", "label": "6: 6号预存位"}, {"value": "7", "label": "7: 废弃位"}]}]}, {"id": 13, "label": "开门测试", "startCommandLabel": "开门启动", "commandKey": "kaimen", "flowNumber": "13", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "柜号", "type": "number_input", "defaultValue": "3"}, {"key": "deviceParams2", "label": "开门状态", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 关门"}, {"value": "1", "label": "1: 开门"}]}]}, {"id": 14, "label": "吸管测试", "startCommandLabel": "吸管启动", "commandKey": "xiguan", "flowNumber": "14", "parameters": [{"key": "deviceParams", "label": "启动状态", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 关闭吸管启动掉落"}, {"value": "1", "label": "1: 吸管启动调落测试"}]}]}, {"id": 15, "label": "全流程", "startCommandLabel": "全流程启动", "commandKey": "quanliucheng", "flowNumber": "15", "parameters": [{"key": "deviceParams", "label": "启动状态", "type": "select", "defaultValue": "0", "options": [{"value": "0", "label": "0: 关闭"}, {"value": "1", "label": "1: 启动"}]}, {"key": "order", "label": "配方", "type": "text_input"}]}]}, {"groupName": "出水测试", "controls": [{"id": 16, "label": "出热水测试", "startCommandLabel": "出热水启动", "commandKey": "<PERSON><PERSON><PERSON>", "flowNumber": "16", "parameters": [{"key": "deviceParams", "label": "出水时间", "type": "number_input", "defaultValue": "5"}]}, {"id": 17, "label": "出冷水测试", "startCommandLabel": "出冷水启动", "commandKey": "chulengshu<PERSON>", "flowNumber": "17", "parameters": [{"key": "deviceParams", "label": "出水时间", "type": "number_input", "defaultValue": "8"}]}]}, {"groupName": "研磨器", "controls": [{"id": 18, "label": "磨豆测试", "startCommandLabel": "磨豆启动", "commandKey": "modou", "flowNumber": "18", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "磨豆量", "type": "number_input", "defaultValue": "16"}, {"key": "deviceParams2", "label": "磨豆时间", "type": "number_input", "defaultValue": "10"}]}]}, {"groupName": "粉仓机构", "controls": [{"id": 19, "label": "出粉测试", "startCommandLabel": "出粉启动", "commandKey": "chufen", "flowNumber": "19", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "粉仓号", "type": "select", "defaultValue": "1", "options": [{"value": "1", "label": "1: 粉仓1"}, {"value": "2", "label": "2: 粉仓2"}, {"value": "3", "label": "3: 粉仓3"}, {"value": "4", "label": "4: 粉仓4"}, {"value": "5", "label": "5: 粉仓5"}, {"value": "6", "label": "6: 粉仓6"}]}, {"key": "deviceParams2", "label": "出粉时间", "type": "number_input", "defaultValue": "10"}]}]}, {"groupName": "果酱仓机构", "controls": [{"id": 20, "label": "果酱测试", "startCommandLabel": "果酱启动", "commandKey": "guojiang", "flowNumber": "20", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "果酱仓号", "type": "select", "defaultValue": "1", "options": [{"value": "1", "label": "1: 果酱仓1"}, {"value": "2", "label": "2: 果酱仓2"}, {"value": "3", "label": "3: 果酱仓3"}, {"value": "4", "label": "4: 果酱仓4"}]}, {"key": "deviceParams2", "label": "出果酱时间", "type": "number_input", "defaultValue": "10"}]}]}, {"groupName": "清洗测试", "controls": [{"id": 21, "label": "清洗测试", "startCommandLabel": "清洗启动", "commandKey": "qingxi", "flowNumber": "21", "parameters": [{"key": "data", "label": "参数个数", "type": "number_input", "defaultValue": "2"}, {"key": "deviceParams1", "label": "清洗号", "type": "select", "defaultValue": "1", "options": [{"value": "1", "label": "1: 废水桶"}, {"value": "2", "label": "2: 锅炉"}, {"value": "3", "label": "3: 注液器"}, {"value": "4", "label": "4: 冲泡器"}, {"value": "5", "label": "5: 搅拌器1"}, {"value": "6", "label": "6: 搅拌器2"}, {"value": "7", "label": "7: 搅拌器3"}, {"value": "8", "label": "8: 搅拌器4"}, {"value": "9", "label": "9: 搅拌器5"}, {"value": "10", "label": "10: 搅拌器6"}]}, {"key": "deviceParams2", "label": "清洗时间", "type": "number_input", "defaultValue": "10"}]}]}, {"groupName": "空调", "controls": [{"id": 22, "label": "空调测试", "startCommandLabel": "空调启动", "commandKey": "kong<PERSON>o", "flowNumber": "22", "parameters": [{"key": "deviceParams", "label": "运行时间", "type": "number_input", "defaultValue": "10"}]}]}]