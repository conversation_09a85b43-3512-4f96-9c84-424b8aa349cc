<template>
  <Teleport to="body">
    <div v-if="visible" class="popup-container">
      <div class="popup-content">
        <slot></slot>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:visible', 'close'])

// const handleClose = () => {
//   if (props.closeOnClickOutside) {
//     emit('update:visible', false)
//     emit('close')
//   }
// }

// Method to show the popup
const show = () => {
  emit('update:visible', true)
}

// Method to hide the popup
const hide = () => {
  emit('update:visible', false)
  emit('close')
}

defineExpose({
  show,
  hide,
})
</script>

<style scoped>
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-content {
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  border-radius: 12px;
}
</style>
