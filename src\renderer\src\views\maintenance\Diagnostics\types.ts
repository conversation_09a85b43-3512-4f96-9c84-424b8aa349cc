export interface SingleStepCommand {
  groupName: string // 机构名称，用于分组显示
  controls: {
    id: number // 对应 Excel 中的序号，作为唯一标识符
    label: string // 对应 Excel 中的功能/内部功能流程，作为UI上的标签
    type: string // 建议的UI组件类型 (e.g., "switch", "button", "input", "select")
    valueDescription: {
      // 描述0和1代表的含义
      '0': string // 0对应的文本描述
      '1': string // 1对应的文本描述
    }
  }[]
}

export interface FlowStepCommand {
  groupName: string // 机构名称，用于分组
  controls: {
    id: number // 序号，唯一标识符
    label: string // 功能描述，作为 UI 标签
    startCommandLabel: string // 内部功能流程的启动命令部分
    parameterLabel: string // 内部功能流程的参数标签部分
    commandKey: string // 示例数据中的 key，作为内部命令标识符
    parameterType: 'number_input' | 'text_input' | 'select' // 根据参数推断的输入类型
    defaultValue: string // 示例数据中的 value，作为默认参数值
    options?: { value: string; label: string }[] // 'select' 类型需要的选项
    description?: string // 数据说明或备注
  }[]
}
