<template>
  <div class="relative w-full h-full">
    <BackWidget v-if="isAllPickup" :time="30" />
    <template v-if="orderList.length">
      <Waiting
        v-if="productOrderInfo.makeStatus?.toString() === MakeStatus.WAITING"
        :description="productOrderInfo.bottomMakeDes"
        class="mt-[250px]"
      />
      <template v-else>
        <Processing
          ref="processingRef"
          :data="productOrderInfo"
          :description="productOrderInfo.topMakeDes"
        />
        <div class="w-full relative">
          <div
            class="text-[#6d3202] text-[20px] absolute top-0 right-[40px] flex items-center gap-2"
          >
            <img src="./imgs/cup.png" style="width: 16px" />
            <span>请在右侧取吸管</span>
          </div>
          <div class="text-[#6d3202] text-[28px] mt-[20px] font-bold text-center">
            {{ productOrderInfo.middleMakeDes }}
          </div>
          <div class="text-[#F25555] text-[20px] text-center">
            {{ productOrderInfo.bottomMakeDes }}
          </div>
          <div v-if="canOpenCabinet" class="flex flex-col items-center gap-2 mt-14">
            <div class="text-[28px] text-gray-900 font-bold">
              取货口：{{ productOrderInfo.pickupPort }}
            </div>
            <button
              class="bg-[#6D3202] h-15 w-[180px] flex items-center justify-center text-white rounded-full font-bold text-[20px] transition-all duration-200 active:scale-[0.95]"
              @click="openCabinet"
            >
              立即开柜
            </button>
          </div>
          <button
            v-if="isAllPickup"
            class="m-auto bg-[#6D3202] h-15 w-[180px] flex items-center justify-center text-white rounded-full font-bold text-[20px] transition-all duration-200 active:scale-[0.95] mt-14"
            @click="handleBack"
          >
            回到首页
          </button>
        </div>
      </template>
      <div class="absolute bottom-0 left-0 w-full">
        <OrderList :data="orderList" />
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import OrderList from './components/OrderList.vue'
import Waiting from './components/Waiting.vue'
import Processing from './components/Processing.vue'
import { queryOrderDetail, openBoxDoor } from '@/api/consumer'
import { useRoute, useRouter } from 'vue-router'
import { ref, computed } from 'vue'
import type { OrderItemType } from './components/OrderList.vue'
import BackWidget from '@/views/consumer/components/BackWidget.vue'
import { toast } from '@/libs/toast'
import { useIntervalFn } from '@vueuse/core'
import { MakeStatus, OrderStatus, SingleOrderStatus } from '@/types/consumer'
const processingRef = ref<InstanceType<typeof Processing>>()
const route = useRoute()
const router = useRouter()
// 订单id
const orderId = route.query.orderId as string
const sign = route.query.sign as string

// 订单中的商品列表
const orderList = ref<OrderItemType[]>([])
// 订单详情
const productOrderInfo = ref<Record<string, string>>({})

// 是否全部取货完成
const isAllPickup = computed(() => {
  return (
    productOrderInfo.value?.makeStatus?.toString() === MakeStatus.COMPLETED &&
    productOrderInfo.value?.orderStatus?.toString() === OrderStatus.COMPLETED
  )
})

// 是否可以开柜
const canOpenCabinet = computed(() => {
  return (
    [MakeStatus.PARTIAL, MakeStatus.COMPLETED].includes(
      productOrderInfo.value?.makeStatus?.toString() as MakeStatus,
    ) && productOrderInfo.value?.orderStatus?.toString() !== OrderStatus.COMPLETED
  )
})

function updateOrderStatus() {
  const orderStatusList = orderList.value.map((item) => item.orderStatus.toString())
  if (orderStatusList.includes(SingleOrderStatus.IN_PROGRESS)) {
    processingRef.value?.resumeProgress()
    return
  }

  if (orderStatusList.includes(SingleOrderStatus.PENDING)) {
    processingRef.value?.resetProgress()
    return
  }
  processingRef.value?.completeStep()
}

function updateOrderDetail() {
  queryOrderDetail(sign).then(({ data }) => {
    if (data.value?.success) {
      const { productOrderInfo: _productOrderInfo, productOrderDetailInfo } = data.value.data
      orderList.value = productOrderDetailInfo
      productOrderInfo.value = _productOrderInfo
      updateOrderStatus()
    }
  })
}
// 获取订单详情
updateOrderDetail()
// 每5秒刷新一次订单详情
useIntervalFn(updateOrderDetail, 5000)

const openCabinet = () => {
  openBoxDoor({ orderId }).then(({ data }) => {
    if (data.value?.success) {
      toast('开柜成功，请及时取餐')
    } else {
      toast('开柜失败，请稍后再试')
    }
  })
}

const handleBack = () => {
  router.replace('/consumer/home')
}
</script>
