<template>
  <div class="h-screen consumer-layout flex flex-col select-none">
    <!-- 消费者界面顶部 -->
    <div class="flex items-center justify-between h-[60px] p-4 text-white">
      <div class="flex items-center gap-2">
        <img src="./imgs/logo.png" class="w-[120px] -mt-1" />
        <div class="text-[28px] font-bold leading-none">蓝奥智饮咖啡</div>
      </div>
      <div class="text-[28px] font-bold leading-none flex items-center gap-2">
        <span>{{ carName }}</span>
        <div
          class="flex items-center justify-center text-[20px]"
          :style="{ color: online ? '#00C290' : '#FF0000' }"
        >
          <Wifi />
          <span class="ml-1">{{ latency === 'unknown' || !online ? '离线' : latency + 'ms' }}</span>
        </div>
      </div>
      <div ref="secret" class="flex items-center gap-2">
        <div class="w-[28px] h-[28px] text-sm font-bold leading-none">客服电话</div>
        <div class="font-bold text-[28px] leading-none">{{ consumerPhone }}</div>
      </div>
    </div>
    <div class="flex w-full p-[40px] flex-1 h-full gap-4">
      <div class="flex flex-col gap-4">
        <div
          class="flex flex-col gap-4 w-[340px] flex-1 bg-white rounded-2xl justify-center items-center gap-8"
        >
          <QRCode :value="qrcode" :size="240" label="微信扫码购买" />
          <div class="text-center text-sm text-gray-500 flex items-center gap-2">
            <img src="./imgs/wechat.png" class="w-[32px]" />
            <span class="text-2xl text-gray-900 font-bold">微信扫码购买</span>
          </div>
        </div>
        <Investment />
      </div>
      <router-view class="flex-1 rounded-2xl bg-white h-full" />
    </div>
  </div>
</template>
<script setup lang="ts">
import QRCode from '@/components/QRCode.vue'
import Investment from '@/views/consumer/components/Investment.vue'
import { onLongPress, useIntervalFn, useOnline } from '@vueuse/core'
import { useTemplateRef, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCarName } from '@/api/consumer'
import { generateSign } from '@/libs/crypto'
import Wifi from './imgs/wifi.svg'
import { useActivityCheck } from '@/hooks/useActivityCheck'
const router = useRouter()
const secret = useTemplateRef<HTMLElement>('secret')
const carId = ref(localStorage.getItem('carId'))
const carName = ref('蓝奥智饮咖啡车')
const qrcode = ref('')
const consumerPhone = ref(import.meta.env.VITE_CONSUMER_PHONE)
const online = useOnline()
// 延迟
const latency = ref<string | number>(-1)
const { updateParams } = useActivityCheck()

onMounted(() => {
  getCarName({ carId: carId.value }).then(({ data }) => {
    if (data.value?.success) {
      carName.value = data.value.data.carName
      localStorage.setItem('carInfo', JSON.stringify(data.value.data))
    }
  })
})
// 更新二维码
function updateQrcode() {
  const timestamp = Date.now()
  if (carId.value) {
    const sign = generateSign(carId.value, timestamp)
    qrcode.value = `${import.meta.env.VITE_MINI_PROGRAM_URL}/home?c=${carId.value}&t=${timestamp}&s=${sign}`
    updateParams({ carId: carId.value, timestamp: timestamp.toString() })
  }
}

updateQrcode()

// 长按进入运维模式入口
onLongPress(
  secret,
  () => {
    router.push('/maintenance/login')
  },
  { delay: 3000 },
)

const checkLatency = () => {
  if (!window.api?.checkLatency) return
  window.api?.checkLatency().then((res) => {
    latency.value = res.time
  })
}
useIntervalFn(updateQrcode, 1000 * 60)
useIntervalFn(checkLatency, 1000 * 5)
checkLatency()
</script>

<style scoped>
.consumer-layout {
  background: linear-gradient(180deg, #070960 0%, #240257 100%);
}
</style>
