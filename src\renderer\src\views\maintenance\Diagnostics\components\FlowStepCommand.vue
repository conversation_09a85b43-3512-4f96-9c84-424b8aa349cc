<template>
  <div class="debug-container">
    <!-- 左侧侧边栏 -->
    <div class="sidebar-wrapper">
      <div class="sidebar-card">
        <div
          v-for="(group, index) in deviceGroups"
          :key="index"
          class="sidebar-item"
          :class="{ active: activeGroup === index }"
          @click="scrollToGroup(index)"
        >
          <span class="item-text">{{ group.groupName }}</span>
          <div class="item-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 右侧内容面板 -->
    <div ref="contentPanel" class="content-panel" @scroll="handleScroll">
      <div
        v-for="(group, index) in deviceGroups"
        :key="index"
        :ref="
          (el) => {
            if (el) groupRefs[index] = el
          }
        "
        class="group-section"
      >
        <h2 class="group-title">{{ group.groupName }}</h2>
        <div class="controls-container">
          <div v-for="control in group.controls" :key="control.id" class="control-item">
            <div class="control-label">{{ control.label }}</div>
            <div class="control-content">
              <!-- 参数输入区域 -->
              <div class="parameters-section">
                <div
                  v-for="parameter in control.parameters"
                  :key="parameter.key"
                  class="parameter-item"
                >
                  <label>{{ parameter.label }}:</label>
                  <!-- 根据参数类型渲染不同的输入方式 -->
                  <div class="parameter-input">
                    <!-- 数字输入 -->
                    <input
                      v-if="parameter.type === 'number_input'"
                      v-model="controlParameters[control.id][parameter.key]"
                      type="number"
                      :placeholder="parameter.defaultValue"
                      @blur="handleBlur(control.id, parameter.key)"
                    />

                    <!-- 文本输入 -->
                    <input
                      v-else-if="parameter.type === 'text_input'"
                      v-model="controlParameters[control.id][parameter.key]"
                      type="text"
                      :placeholder="parameter.defaultValue"
                    />

                    <!-- 下拉选择 -->
                    <select
                      v-else-if="parameter.type === 'select'"
                      v-model="controlParameters[control.id][parameter.key]"
                    >
                      <option
                        v-for="option in parameter.options"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 启动按钮 -->
              <button
                class="start-button"
                :disabled="controlLoading[control.id]"
                @click="handleStartCommand(control)"
              >
                {{ controlLoading[control.id] ? '发送中...' : control.startCommandLabel }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import deviceData from '../FlowCommand.json'
import { useWebSocketConfig } from '@/views/maintenance/hooks/useWebSocketConfig'
import { toast } from '@/libs/toast'

const { send } = useWebSocketConfig()

// 设备组数据
const deviceGroups = ref(deviceData)

// 控制器参数值
const controlParameters = reactive({})

// 控制器loading状态
const controlLoading = reactive({})

// 初始化控制器参数
deviceGroups.value.forEach((group) => {
  group.controls.forEach((control) => {
    controlParameters[control.id] = {}
    controlLoading[control.id] = false
    control.parameters.forEach((parameter) => {
      controlParameters[control.id][parameter.key] = parameter.defaultValue
    })
  })
})

// 当前激活的组索引
const activeGroup = ref(0)

// 组元素引用集合
const groupRefs = reactive([])
const contentPanel = ref(null)

// 是否正在滚动 (避免滚动事件循环)
const isScrolling = ref(false)

// 启动命令处理
const handleStartCommand = (control) => {
  // 如果正在loading，直接返回
  if (controlLoading[control.id]) {
    return
  }

  const parameters = controlParameters[control.id]
  console.log(`执行命令: ${control.commandKey}, 参数:`, parameters)

  // 设置loading状态
  controlLoading[control.id] = true

  try {
    // 构建flowDebug消息
    const message = {
      type: 'flowDebug',
      machineNumber: '1',
      data: {
        flowNumber: control.flowNumber,
        ...parameters,
      },
    }

    const res = send(JSON.stringify(message))
    if (res) {
      toast('命令发送成功')
    } else {
      toast('命令发送失败', 'error')
    }
  } catch (error) {
    toast('命令发送异常', 'error')
  } finally {
    // 无论成功失败都要清除loading状态
    controlLoading[control.id] = false
  }
}

// 处理数字输入框失去焦点时，将值转换为字符串
const handleBlur = (controlId, parameterKey) => {
  controlParameters[controlId][parameterKey] = controlParameters[controlId][parameterKey].toString()
}

// 滚动到指定组
const scrollToGroup = (index) => {
  if (isScrolling.value) return

  isScrolling.value = true
  activeGroup.value = index

  if (groupRefs[index]) {
    groupRefs[index].scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })

    // 滚动完成后重置标志
    setTimeout(() => {
      isScrolling.value = false
    }, 500)
  }
}

// 处理滚动事件，更新当前激活的组
const handleScroll = () => {
  if (isScrolling.value) return

  // 找到当前可见的组
  for (let i = 0; i < groupRefs.length; i++) {
    const el = groupRefs[i]
    if (!el) continue

    const rect = el.getBoundingClientRect()
    const elTop = rect.top
    const panelTop = contentPanel.value.getBoundingClientRect().top
    const relativeTop = elTop - panelTop

    // 如果元素顶部在视口内或刚好在顶部之上
    if (relativeTop <= 50 && relativeTop + rect.height > 0) {
      activeGroup.value = i
      break
    }
  }
}

onMounted(() => {
  // 确保所有引用都已填充
  groupRefs.length = deviceGroups.value.length
})
</script>

<style scoped>
.debug-container {
  display: flex;
  height: calc(100vh - 120px);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-wrapper {
  width: 180px;
  background-color: #f5f7fa;
  padding: 20px 15px;
  overflow-y: auto;
}

.sidebar-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.sidebar-item {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  border-bottom: 1px solid #f0f2f5;
}

.sidebar-item:last-child {
  border-bottom: none;
}

.sidebar-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.sidebar-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
}

.item-text {
  display: block;
  line-height: 1.4;
}

.item-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
}

.sidebar-item.active .item-indicator {
  background-color: #409eff;
}

.content-panel {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.group-section {
  padding: 25px 0;
  margin-bottom: 0;
}

.group-title {
  margin-top: 0;
  margin-bottom: 20px;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 18px;
  color: #303133;
  letter-spacing: 0.5px;
}

.controls-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 15px;
}

.control-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.control-label {
  font-weight: 500;
  color: #303133;
  font-size: 16px;
  margin-bottom: 10px;
}

.control-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.parameters-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.parameter-input {
  margin-bottom: 2px;
}

.parameter-input input,
.parameter-input select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
}

.parameter-input input:focus,
.parameter-input select:focus {
  outline: none;
  border-color: #409eff;
}

.start-button {
  padding: 10px 15px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s;
  margin-top: 8px;
}

.start-button:hover {
  background-color: #66b1ff;
}

.start-button:active {
  background-color: #3a8ee6;
}

.start-button:disabled {
  background-color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.6;
}

.start-button:disabled:hover {
  background-color: #c0c4cc;
}

@media (max-width: 768px) {
  .debug-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar-wrapper {
    width: 100%;
    padding: 15px;
  }

  .sidebar-card {
    display: flex;
    overflow-x: auto;
  }

  .sidebar-item {
    padding: 10px 15px;
    white-space: nowrap;
    border-bottom: none;
    border-right: 1px solid #f0f2f5;
    flex-shrink: 0;
  }

  .sidebar-item:last-child {
    border-right: none;
  }

  .item-indicator {
    left: 0;
    right: 0;
    top: auto;
    bottom: 0;
    width: auto;
    height: 3px;
  }

  .controls-container {
    grid-template-columns: 1fr;
  }

  .control-item {
    padding: 12px;
  }
}

/* 针对户外强光环境优化的额外样式 */
@media (max-width: 1024px) {
  .control-item {
    background-color: #f0f2f5;
  }
}
</style>
