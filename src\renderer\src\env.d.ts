/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 边缘滑动手势API返回类型
interface EdgeSwipeCheckResult {
  disabled?: boolean
  error?: string
}

interface EdgeSwipeOperationResult {
  success: boolean
  message?: string
  error?: string
}

interface EdgeSwipeSetResult {
  wasAlreadySet: boolean
  success: boolean
  message: string
}

interface ApiMethods {
  // 虚拟键盘相关
  openVirtualKeyboard: () => void
  closeVirtualKeyboard: () => void
  // 窗口相关
  getWindowId: () => Promise<number>
  enterKiosk: () => Promise<boolean>
  exitKiosk: () => Promise<boolean>
  // 自启动相关
  setAutoLaunch: (enable: boolean) => Promise<any>
  getAutoLaunch: () => Promise<any>
  // 网络相关
  checkLatency: (url: string) => Promise<{ time?: number; alive?: boolean; error?: string }>
  // 边缘滑动手势相关
  checkEdgeSwipeDisabled: () => Promise<EdgeSwipeCheckResult>
  disableEdgeSwipe: () => Promise<EdgeSwipeOperationResult>
  enableEdgeSwipe: () => Promise<EdgeSwipeOperationResult>
  checkAndSetEdgeSwipeDisabled: () => Promise<EdgeSwipeSetResult>
}

declare global {
  interface Window {
    api: ApiMethods
  }
}
