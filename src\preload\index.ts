import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
// Custom APIs for renderer
const api = {
  // 添加虚拟键盘相关API
  openVirtualKeyboard: () => ipcRenderer.send('open-virtual-keyboard'),
  closeVirtualKeyboard: () => ipcRenderer.send('close-virtual-keyboard'),
  // 添加获取窗口ID的方法
  getWindowId: () => ipcRenderer.invoke('get-window-id'),
  // 添加kiosk模式控制API
  enterKiosk: () => ipcRenderer.invoke('enter-kiosk'),
  exitKiosk: () => ipcRenderer.invoke('exit-kiosk'),
  // 添加开机自启动控制API
  setAutoLaunch: (enable: boolean) => ipcRenderer.invoke('set-auto-launch', enable),
  getAutoLaunch: () => ipcRenderer.invoke('get-auto-launch'),
  // 添加检查网络延迟API
  checkLatency: (url: string) => ipcRenderer.invoke('check-latency', url),
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
