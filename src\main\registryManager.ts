import regedit from 'regedit'

// 注册表路径
const EDGE_UI_KEY = '计算机\\HKEY_LOCAL_MACHIN\\SOFTWARE\\Policies\\Microsoft\\Windows\\SettingSync'
const ALLOW_EDGE_SWIPE_VALUE = 'AllowEdgeSwipe'

/**
 * 检查注册表中是否已设置禁用边缘滑动手势
 */
export async function checkEdgeSwipeDisabled(): Promise<boolean> {
  return new Promise((resolve) => {
    regedit.list([EDGE_UI_KEY], (err, result) => {
      console.log('result', result)
      if (err) {
        console.error('读取注册表失败:', err)
        resolve(false)
        return
      }

      try {
        const edgeUIKey = result[EDGE_UI_KEY]

        // 检查键是否存在
        if (!edgeUIKey || !edgeUIKey.exists) {
          console.log('EdgeUI注册表项不存在')
          resolve(false)
          return
        }

        // 检查AllowEdgeSwipe值是否存在且为0
        const values = edgeUIKey.values
        if (values && values[ALLOW_EDGE_SWIPE_VALUE]) {
          const allowEdgeSwipeValue = values[ALLOW_EDGE_SWIPE_VALUE]
          const isDisabled = allowEdgeSwipeValue.value === 0
          console.log(
            `边缘滑动手势状态: ${isDisabled ? '已禁用' : '已启用'} (值: ${allowEdgeSwipeValue.value})`,
          )
          resolve(isDisabled)
        } else {
          console.log('AllowEdgeSwipe值不存在')
          resolve(false)
        }
      } catch (error) {
        console.error('解析注册表结果失败:', error)
        resolve(false)
      }
    })
  })
}

/**
 * 禁用边缘滑动手势
 */
export async function disableEdgeSwipe(): Promise<boolean> {
  return new Promise((resolve) => {
    // 先创建键，确保路径存在
    regedit.createKey([EDGE_UI_KEY], (createErr) => {
      if (createErr) {
        console.error('创建注册表键失败:', createErr)
        resolve(false)
        return
      }

      // 设置值
      const registryData = {
        [EDGE_UI_KEY]: {
          [ALLOW_EDGE_SWIPE_VALUE]: {
            value: 0,
            type: 'REG_DWORD',
          },
        },
      }

      // @ts-ignore - regedit类型定义有问题，REG_DWORD应该支持number类型
      regedit.putValue(registryData, (putErr) => {
        if (putErr) {
          console.error('设置注册表失败:', putErr)
          resolve(false)
        } else {
          console.log('成功禁用边缘滑动手势')
          resolve(true)
        }
      })
    })
  })
}

/**
 * 启用边缘滑动手势
 */
export async function enableEdgeSwipe(): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const registryData = {
      [EDGE_UI_KEY]: {
        [ALLOW_EDGE_SWIPE_VALUE]: {
          value: 1,
          type: 'REG_DWORD',
        },
      },
    }

    regedit.putValue(registryData, (err) => {
      if (err) {
        console.error('设置注册表失败:', err)
        resolve(false)
      } else {
        console.log('成功启用边缘滑动手势')
        resolve(true)
      }
    })
  })
}

/**
 * 检查并自动设置禁用边缘滑动手势
 */
export async function checkAndSetEdgeSwipeDisabled(): Promise<{
  wasAlreadySet: boolean
  success: boolean
  message: string
}> {
  try {
    // 检查当前状态
    const isAlreadyDisabled = await checkEdgeSwipeDisabled()

    if (isAlreadyDisabled) {
      return {
        wasAlreadySet: true,
        success: true,
        message: '边缘滑动手势已经被禁用',
      }
    }

    // 如果没有设置，则设置禁用
    const setResult = await disableEdgeSwipe()

    if (setResult) {
      return {
        wasAlreadySet: false,
        success: true,
        message: '成功设置禁用边缘滑动手势，重启后生效',
      }
    } else {
      return {
        wasAlreadySet: false,
        success: false,
        message: '设置注册表失败，请检查管理员权限',
      }
    }
  } catch (error) {
    console.error('检查和设置边缘滑动手势失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return {
      wasAlreadySet: false,
      success: false,
      message: `操作失败: ${errorMessage}`,
    }
  }
}
