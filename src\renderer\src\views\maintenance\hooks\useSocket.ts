import { ref } from 'vue'
import { useWebSocket } from '@vueuse/core'

// 单例实例
let wsInstance: ReturnType<typeof createWebSocketInstance> | null = null

export interface WebSocketOptions {
  onConnected?: (ws: WebSocket) => void
  onDisconnected?: (ws: WebSocket, event: CloseEvent) => void
  onError?: (ws: WebSocket, event: Event) => void
  onMessage?: (ws: WebSocket, event: MessageEvent) => void
  heartbeat?:
    | boolean
    | {
        message?: string
        interval?: number
        pongTimeout?: number
      }
  autoReconnect?:
    | boolean
    | {
        retries?: number | (() => boolean)
        delay?: number
        onFailed?: () => void
      }
  protocols?: string[]
}

// 创建 WebSocket 实例的工厂函数
function createWebSocketInstance(serverUrl: string, options: WebSocketOptions = {}) {
  const url = ref(serverUrl)
  console.log('url', url.value)
  const {
    data,
    status,
    send: sendMessage,
    open,
    close,
    ws,
  } = useWebSocket(url, {
    autoReconnect: options.autoReconnect || false,
    heartbeat: options.heartbeat || false,
    protocols: options.protocols || [],
    onConnected: options.onConnected,
    onDisconnected: options.onDisconnected,
    onError: options.onError,
    onMessage: options.onMessage,
  })

  // 扩展方法：更新 URL 并重新连接
  const updateUrl = (newUrl: string) => {
    url.value = newUrl
    // URL 变更会自动重连，因为 useWebSocket 的 autoConnect 默认为 true
  }

  return {
    data,
    status,
    send: sendMessage,
    open,
    close,
    ws,
    updateUrl,
  }
}

// 公用的 useSocket hook
export function useSocket(serverUrl?: string, options: WebSocketOptions = {}) {
  // 如果没有实例或提供了新的 URL，则创建新实例
  console.log('serverUrl', serverUrl)
  if (!wsInstance && serverUrl) {
    wsInstance = createWebSocketInstance(serverUrl, options)
  } else if (serverUrl && wsInstance) {
    // 如果已有实例且提供了新 URL，则更新 URL
    wsInstance.updateUrl(serverUrl)
  } else if (!wsInstance && !serverUrl) {
    throw new Error('WebSocket URL is required for the first initialization')
  }

  return wsInstance!
}

// 重置/清除 WebSocket 实例
export function resetWebSocket() {
  if (wsInstance) {
    wsInstance.close()
    wsInstance = null
  }
}
