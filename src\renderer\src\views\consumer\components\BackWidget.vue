<template>
  <div class="flex w-full justify-between absolute top-0 left-0 p-[20px] z-10">
    <div
      class="flex flex-col justify-center items-center w-[80px] h-[80px] bg-white rounded-full shadow-lg rounded-full transition-all duration-200 active:scale-[0.95]"
      @click="handleBack"
    >
      <img src="./imgs/back.png" class="w-[24px] h-[21px]" />
      <span class="text-[24px] text-gray-900 font-bold">返回</span>
    </div>
    <div class="flex justify-center items-center w-[80px] h-[80px] bg-white rounded-full shadow-lg">
      {{ time }}S
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useCountdown } from '@/hooks/useCountdown'
const props = defineProps<{ time?: number }>()
const router = useRouter()
const { time, pause, resume } = useCountdown(props.time)

const handleBack = () => {
  router.back()
}
defineExpose({ pause, resume })
</script>
