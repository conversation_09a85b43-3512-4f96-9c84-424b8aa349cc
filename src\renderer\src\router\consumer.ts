import ConsumerLayout from '../layouts/ConsumerLayout.vue'

export default [
  {
    path: '/consumer',
    component: ConsumerLayout,
    children: [
      {
        path: 'home',
        name: 'ConsumerHome',
        component: () => import('../views/consumer/Home.vue'),
        meta: { title: '欢迎' },
      },
      {
        path: 'goods-details',
        name: 'GoodsDetails',
        component: () => import('../views/consumer/goods-menu/details/index.vue'),
        meta: { title: '商品详情' },
      },
      {
        path: 'payments',
        name: 'ConsumerPayments',
        component: () => import('../views/consumer/goods-menu/payments/index.vue'),
        meta: { title: '支付' },
      },
      {
        path: 'processing',
        name: 'ConsumerProcessing',
        component: () => import('../views/consumer/processing/index.vue'),
        meta: { title: '制作中' },
      },
    ],
  },
]
