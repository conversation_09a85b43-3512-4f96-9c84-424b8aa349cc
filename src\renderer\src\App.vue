<script setup lang="ts">
import 'swiper/css'
import 'swiper/css/free-mode'
import 'swiper/css/scrollbar'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
</script>

<template>
  <router-view />
</template>

<style>
#app {
  height: 100vh;
}
/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
  transition: background 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  height: auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
</style>
