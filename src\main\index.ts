import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { exec } from 'child_process'
import AutoLaunch from 'auto-launch'
import ping from 'ping'
// import { checkEdgeSwipeDisabled, disableEdgeSwipe } from './registryManager'

// 网络状态管理
let isOnline = false
let mainWindow: BrowserWindow | null = null
let networkCheckInterval: NodeJS.Timeout | null = null

// 在线地址配置
const ONLINE_URL = 'http://**************:9027/#/consumer/home'
const PING_HOST = '**************'
const NETWORK_CHECK_INTERVAL = 5000 // 5秒检查一次

// 网络状态检测函数
async function checkNetworkStatus(): Promise<boolean> {
  try {
    const res = await ping.promise.probe(PING_HOST, {
      timeout: 3,
      extra: ['-c', '1'],
    })
    return res.alive
  } catch (error) {
    console.error('网络检测失败:', error)
    return false
  }
}

// 更新网络状态并通知渲染进程
async function updateNetworkStatus(): Promise<void> {
  const newStatus = await checkNetworkStatus()

  if (newStatus !== isOnline) {
    isOnline = newStatus
    console.log(`网络状态变化: ${isOnline ? '在线' : '离线'}`)

    // 通知渲染进程网络状态变化
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('network-status-changed', isOnline)

      // 如果网络恢复，重新加载在线地址
      if (isOnline) {
        console.log('网络恢复，加载在线地址')
        mainWindow.loadURL(ONLINE_URL)
      }
    }
  }
}

// 启动网络状态监听
function startNetworkMonitoring(): void {
  if (networkCheckInterval) {
    clearInterval(networkCheckInterval)
  }

  networkCheckInterval = setInterval(updateNetworkStatus, NETWORK_CHECK_INTERVAL)
}

// 停止网络状态监听
function stopNetworkMonitoring(): void {
  if (networkCheckInterval) {
    clearInterval(networkCheckInterval)
    networkCheckInterval = null
  }
}

// 添加打开和关闭虚拟键盘的函数
function openVirtualKeyboard(): void {
  if (process.platform === 'win32') {
    exec('start osk.exe')
  }
}

function closeVirtualKeyboard(): void {
  if (process.platform === 'win32') {
    exec('taskkill /f /im osk.exe')
  }
}

async function createWindow(): Promise<void> {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    kiosk: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
    },
  })

  mainWindow.on('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show()
      mainWindow.setFullScreen(true)
    }
  })

  mainWindow.on('closed', () => {
    stopNetworkMonitoring()
    mainWindow = null
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    // 检查网络状态并决定加载哪个页面
    isOnline = await checkNetworkStatus()
    console.log(`初始网络状态: ${isOnline ? '在线' : '离线'}`)

    if (isOnline) {
      mainWindow.loadURL(ONLINE_URL)
    } else {
      mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }

    // 启动网络状态监听
    startNetworkMonitoring()
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // 检查边缘滑动手势设置（仅在Windows系统）
  /* if (process.platform === 'win32') {
    try {
      console.log('检查边缘滑动手势设置...')
      const isDisabled = await checkEdgeSwipeDisabled()

      if (!isDisabled) {
        // 显示确认对话框
        const response = await dialog.showMessageBox({
          type: 'question',
          buttons: ['是', '否'],
          defaultId: 0,
          title: '边缘滑动手势设置',
          message: '检测到边缘滑动手势未被禁用',
          detail:
            '为了更好的应用体验，建议禁用Windows边缘滑动手势。\n\n此操作需要修改注册表，完成后将自动重启应用。\n\n是否继续？',
        })

        if (response.response === 0) {
          // 用户点击"是"
          console.log('用户确认禁用边缘滑动手势')

          try {
            const setResult = await disableEdgeSwipe()

            if (setResult) {
              // 显示成功消息并重启
              await dialog.showMessageBox({
                type: 'info',
                buttons: ['确定'],
                title: '设置成功',
                message: '边缘滑动手势已成功禁用',
                detail: '应用将重新启动以确保设置生效。',
              })

              console.log('设置成功，重启应用...')
              app.relaunch()
              app.exit(0)
              return
            } else {
              // 显示失败消息
              await dialog.showMessageBox({
                type: 'error',
                buttons: ['确定'],
                title: '设置失败',
                message: '无法设置边缘滑动手势',
                detail: '请确保以管理员身份运行此应用程序。',
              })
            }
          } catch (error) {
            console.error('设置边缘滑动手势失败:', error)
            const errorMessage = error instanceof Error ? error.message : String(error)

            await dialog.showMessageBox({
              type: 'error',
              buttons: ['确定'],
              title: '操作失败',
              message: '设置边缘滑动手势时发生错误',
              detail: `错误信息: ${errorMessage}`,
            })
          }
        } else {
          console.log('用户取消禁用边缘滑动手势')
        }
      } else {
        console.log('边缘滑动手势已被禁用，无需设置')
      }
    } catch (error) {
      console.error('检查边缘滑动手势设置失败:', error)
    }
  } */

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // 网络延迟测试
  ipcMain.handle('check-latency', async () => {
    try {
      const res = await ping.promise.probe(PING_HOST)
      return {
        time: res.time,
        alive: res.alive,
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return { error: errorMessage }
    }
  })

  // 添加IPC监听器
  ipcMain.on('open-virtual-keyboard', () => {
    openVirtualKeyboard()
  })

  ipcMain.on('close-virtual-keyboard', () => {
    closeVirtualKeyboard()
  })

  // 添加获取窗口引用的IPC处理
  ipcMain.handle('get-window-id', (event) => {
    return BrowserWindow.fromWebContents(event.sender)?.id
  })

  // 添加kiosk模式控制
  ipcMain.handle('enter-kiosk', (event) => {
    const win = BrowserWindow.fromWebContents(event.sender)
    if (win) {
      win.setKiosk(true)
      return true
    }
    return false
  })

  ipcMain.handle('exit-kiosk', (event) => {
    const win = BrowserWindow.fromWebContents(event.sender)
    if (win) {
      win.setKiosk(false)
      return true
    }
    return false
  })

  // 创建自启动设置
  const autoLauncher = new AutoLaunch({
    name: app.getName(),
    path: process.execPath,
  })

  // 设置自启动
  autoLauncher.enable()

  // 检查是否已经设置了自启动
  autoLauncher.isEnabled().then((isEnabled) => {
    if (isEnabled) {
      console.log('开机自启动已启用')
    } else {
      console.log('开机自启动未启用')
      autoLauncher.enable()
    }
  })

  await createWindow()

  app.on('activate', async function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) await createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
