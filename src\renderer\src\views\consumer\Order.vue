<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useCoffeeStore } from '../../stores/coffee'

const router = useRouter()
const coffeeStore = useCoffeeStore()

const handleSubmitOrder = () => {
  const order = coffeeStore.submitOrder()
  if (order) {
    router.push('/consumer/processing')
  }
}

const getCoffeeName = (coffeeId: string) => {
  return coffeeStore.menu.find((coffee) => coffee.id === coffeeId)?.name || '未知咖啡'
}

const getCoffeePrice = (coffeeId: string) => {
  return coffeeStore.menu.find((coffee) => coffee.id === coffeeId)?.price || 0
}
</script>

<template>
  <div class="container p-4 mx-auto">
    <h1 class="mb-6 text-3xl font-bold text-center">您的订单</h1>

    <div v-if="coffeeStore.currentOrder.length === 0" class="text-center">
      <p class="text-xl text-gray-600">您的订单是空的</p>
      <button
        class="px-6 py-2 mt-4 text-white bg-blue-600 rounded-md hover:bg-blue-700"
        @click="router.push('/consumer/menu')"
      >
        去点咖啡
      </button>
    </div>

    <div v-else>
      <div class="overflow-hidden bg-white rounded-lg shadow">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                商品
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                数量
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                单价
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                小计
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(item, index) in coffeeStore.currentOrder" :key="index">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ getCoffeeName(item.coffeeId) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ item.quantity }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥{{ getCoffeePrice(item.coffeeId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  ¥{{ getCoffeePrice(item.coffeeId) * item.quantity }}
                </div>
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3" class="px-6 py-4 text-right text-sm font-medium">总计:</td>
              <td class="px-6 py-4 font-bold text-xl">¥{{ coffeeStore.totalPrice }}</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div class="flex justify-end gap-4 mt-6">
        <button
          class="px-6 py-2 text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50"
          @click="router.push('/consumer/menu')"
        >
          继续点餐
        </button>
        <button
          class="px-6 py-2 text-white bg-green-600 rounded-md hover:bg-green-700"
          @click="handleSubmitOrder"
        >
          确认订单
        </button>
      </div>
    </div>
  </div>
</template>
