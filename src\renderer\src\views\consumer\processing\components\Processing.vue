<template>
  <div
    class="w-[calc(100%-80px)] m-[40px] bg-[#3c1e08] rounded-[10px] h-[268px] px-[160px] flex flex-col justify-center"
  >
    <!-- 咖啡机图标 -->
    <div class="coffee-machine flex justify-center mb-4">
      <img :src="ProcessingIcon" class="w-[88px]" />
    </div>

    <!-- 当前制作状态提示 -->
    <div class="current-state text-center text-white text-lg">
      {{ description }}
    </div>

    <!-- 进度条和步骤名称 -->
    <div class="progress-bar-container">
      <!-- 进度条 -->
      <div class="relative h-[10px]">
        <!-- 背景进度条 -->
        <div class="absolute top-0 left-0 w-full h-full bg-[#51321A] rounded-full"></div>

        <!-- 主进度条 -->
        <div
          class="absolute top-0 left-0 h-full bg-orange-500 rounded-full transition-all ease-linear duration-300"
          :style="{ width: `${totalProgress}%` }"
        ></div>
      </div>
      <!-- 步骤名称 -->
      <div class="flex items-center justify-between mb-4 mt-2">
        <div v-for="step in steps" :key="step.id" class="relative flex flex-col items-center">
          <span
            class="step-name"
            :class="{
              'text-white': step.id === currentStep,
              'text-[#9F8E81]': step.id !== currentStep,
            }"
          >
            {{ step.name }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useIntervalFn } from '@vueuse/core'
import ProcessingIcon from '../imgs/processing.png'

defineProps<{ description: string }>()

// 定义咖啡制作的步骤
const steps = [
  { id: 1, name: '加入冰块', duration: 1000 },
  { id: 2, name: '研磨咖啡豆', duration: 3000 },
  { id: 3, name: '萃取原液', duration: 4000 },
  { id: 4, name: '加入鲜奶', duration: 1500 },
  { id: 5, name: '加入糖浆', duration: 1500000 },
]

// 定义当前进度
const currentStep = ref(1)
// 定义当前步骤的进度百分比 (0-100)
const currentProgress = ref(0)
// 是否自动播放进度
const autoPlay = ref(false)

// 直接控制步骤完成
const completeStep = () => {
  currentStep.value = steps.length
  currentProgress.value = 100
}

// 计算总进度百分比 (0-100%)
const totalProgress = computed(() => {
  // 计算已完成步骤的进度贡献
  const completedProgress = (currentStep.value - 1) * (100 / steps.length)
  // 计算当前步骤的进度贡献
  const currentStepProgress = (currentProgress.value / 100) * (100 / steps.length)

  return completedProgress + currentStepProgress
})

// 获取当前步骤的配置
const getCurrentStep = computed(() => {
  const index = currentStep.value - 1
  return index >= 0 && index < steps.length ? steps[index] : null
})

// 控制进度更新的间隔 (根据当前步骤的持续时间计算)
const progressUpdateInterval = computed(() => {
  const step = getCurrentStep.value
  if (!step) return 100
  // 将持续时间分成50份，使动画更平滑
  return step.duration / 50
})

// 自动播放功能 - 更新进度条
const {
  pause: pauseProgress,
  resume: resumeProgress,
  isActive: isProgressActive,
} = useIntervalFn(
  () => {
    if (currentProgress.value < 100) {
      currentProgress.value += 2 // 每次增加2%，使动画更平滑
    } else {
      // 当前步骤完成
      if (currentStep.value < steps.length) {
        // 进入下一步骤
        currentStep.value++
        currentProgress.value = 0
      } else {
        // 所有步骤完成
        pauseProgress()
        autoPlay.value = false
      }
    }
  },
  progressUpdateInterval,
  { immediate: false },
)

// 观察当前步骤的变化，调整进度更新频率
watch(currentStep, () => {
  if (isProgressActive.value) {
    pauseProgress()
    resumeProgress()
  }
})

// 控制自动播放
const toggleAutoPlay = () => {
  if (isProgressActive.value) {
    pauseProgress()
    autoPlay.value = false
  } else {
    // 如果已经完成所有步骤，重新开始
    if (currentStep.value >= steps.length && currentProgress.value >= 100) {
      currentStep.value = 1
      currentProgress.value = 0
    }
    resumeProgress()
    autoPlay.value = true
  }
}

// 重置进度
const resetProgress = () => {
  currentStep.value = 1
  currentProgress.value = 0
  if (autoPlay.value) {
    pauseProgress()
    autoPlay.value = false
  }
}

// 自动开始制作过程
onMounted(() => {
  toggleAutoPlay()
})

defineExpose({
  completeStep,
  resetProgress,
  resumeProgress,
})
</script>

<style scoped>
.progress-bar-container {
  margin: 0 1rem;
  padding: 1rem 0.5rem;
  position: relative;
}

.step-name {
  transition: all 0.3s ease;
}
</style>
