<template>
  <div class="flex justify-center w-full h-full relative pt-[180px]">
    <BackWidget />
    <template v-if="goodsDetail">
      <!-- 商品主图 -->
      <GoodsImage
        :src="goodsDetail?.imagePath || ''"
        width="400px"
        height="400px"
        class="rounded-xl mr-10"
      />
      <div v-show="goodsDetail" class="-mt-2">
        <!-- 商品信息 -->
        <div class="px-4 mb-5">
          <div class="text-[28px] text-gray-900 font-bold">{{ goodsDetail?.productName }}</div>
          <div class="text-gray-500 text-[20px] mt-2 block max-w-[400px] line-clamp-3!">
            {{ goodsDetail?.productRemark }}
          </div>
        </div>
        <view v-if="promotion.length > 0" class="px-4 mb-4 flex gap-1 items-center">
          <view
            v-for="item of promotion"
            :key="item.labelContent"
            class="flex items-center justify-between rounded text-sm text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1"
          >
            <text>{{ item.labelContent }}</text>
          </view>
        </view>
        <!-- 规格选择 -->
        <SpecificationSelector
          v-if="goodsDetail"
          v-model:selected-cup="selectedCup"
          v-model:selected-temp="selectedTemp"
          v-model:selected-sweet="selectedSweet"
          v-model:quantity="quantity"
          :data="goodsDetail"
          :full-spec-names="fullSpecNames"
          :cup-price-list="goodsDetail.cupPriceList"
          :sweet-list="goodsDetail.sweetList"
          :is-temperature-disabled="isTemperatureDisabled"
          :is-cup-type-disabled="isCupTypeDisabled"
          :available-temperatures="availableTemperatures"
          :available-cup-types="availableCupTypes"
        />
        <!-- 购物车栏 -->
        <div class="flex flex-col px-4 mt-14">
          <div class="text-[#6D3202]">
            <span class="text-3xl font-semibold">
              <span class="text-lg mr-1">¥</span>
              <span>{{ selectedPromotion || selectedPrice }}</span>
            </span>
            <span
              v-if="selectedPromotion && selectedPromotion !== selectedPrice"
              class="text-sm line-through text-gray-600 ml-1"
            >
              ¥{{ selectedPrice }}
            </span>
          </div>
          <div class="text-[20px] text-gray-600 mt-1">{{ fullSpecNames.join(',') }}</div>
          <div class="text-[20px] text-gray-600 mt-9 flex gap-[10px]">
            <button
              class="w-[180px] bg-[#6D3202] rounded-full px-4 py-2 text-white mt-1 transition-all duration-200 active:scale-[0.95]"
              @click="goToPayment"
            >
              立即结算
            </button>
            <button
              class="w-[180px] border-[#6D3202] border-1 rounded-full px-4 py-2 text-[#6D3202] mt-1 transition-all duration-200 active:scale-[0.95]"
              @click="handleAddToCart"
            >
              加入购物车
            </button>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import SpecificationSelector from './components/SpecificationSelector.vue'
import { useCart } from '../useCart'
import { useGoodsDetail } from './useGoodsDetail'
import { fetchMenuDetailById } from '@/api/consumer'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import GoodsImage from '@/views/consumer/components/GoodsImage.vue'
import BackWidget from '@/views/consumer/components/BackWidget.vue'

const quantity = ref(1)
const router = useRouter()
const { getGoodsDetails, addToCart, backupCart, clearCart } = useCart()
const {
  goodsDetail,
  selectedTemp,
  selectedSweet,
  selectedCup,
  fullSpecNames,
  setGoodsDetail,
  selectedPrice,
  selectedPromotion,
  selectedSpecification,
  isTemperatureDisabled,
  isCupTypeDisabled,
  availableTemperatures,
  availableCupTypes,
  sugarMapData,
} = useGoodsDetail()

const promotion = computed(() => {
  const promotionalList = goodsDetail.value?.promotionalList
  if (!Array.isArray(promotionalList)) return []
  const seen = new Set()
  return promotionalList
    .filter((item) => {
      if (seen.has(item.promotionId)) {
        return false
      }
      seen.add(item.promotionId)
      return true
    })
    .map((item) => {
      return {
        labelContent: item.labelContent,
        promotionalPrice: item.promotionalPrice,
      }
    })
})

onMounted(async () => {
  const route = useRoute()
  const { productId, carId } = route.query
  if (productId) {
    let detail = await getGoodsDetails(productId as string)?.then((res: any) => res?.data)
    if (!detail) {
      const res = await fetchMenuDetailById({
        productId: productId as string,
        carId: carId as string,
      })
      detail = res.data.value.data
    }
    setGoodsDetail(detail)
  }
})

const handleAddToCart = () => {
  addToCart(
    {
      ...goodsDetail.value!,
      price: selectedPrice.value,
      $quantity: quantity.value,
      $specification: selectedSpecification.value,
      $fullSpecNames: fullSpecNames.value,
      $sugarData: sugarMapData.value,
    },
    false,
  )
  router.replace('/consumer/home')
}
const goToPayment = () => {
  backupCart()
  clearCart()
  handleAddToCart()
  router.push('/consumer/payments')
}
</script>
