<template>
  <div class="diagnostics-container">
    <DiagnosticsHeader @change="handleChange" @open-web-socket-config="showWebSocketModal = true" />
    <div class="flex gap-4">
      <div class="flex-1">
        <DiagnosticsInfo :debug-type="debugType" />
      </div>
      <!-- <div class="flex-basis-2/5">
        <DiagnosticsWebsocketLog />
      </div> -->
    </div>

    <!-- WebSocket配置弹窗 -->
    <WebSocketConfigModal v-model:visible="showWebSocketModal" @confirm="handleWebSocketUpdate" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import DiagnosticsHeader from './components/DiagnosticsHeader.vue'
import DiagnosticsInfo from './components/DiagnosticsInfo.vue'
import WebSocketConfigModal from './components/WebSocketConfigModal.vue'
import { useWebSocketConfig } from '@/views/maintenance/hooks/useWebSocketConfig'
// import DiagnosticsWebsocketLog from './components/WebsocketLog.vue'

const debugType = ref(0)
const showWebSocketModal = ref(false)

// 获取WebSocket配置实例
const { reset } = useWebSocketConfig()

const handleChange = (index) => {
  debugType.value = index
}

const handleWebSocketUpdate = (newAddress) => {
  console.log('WebSocket地址已更新为:', newAddress)
  // 这里可以添加额外的处理逻辑，比如通知其他组件
}

// 组件挂载时确保WebSocket连接已初始化
onMounted(() => {
  console.log('诊断页面已挂载，初始化WebSocket连接')
  reset() // 重置并重新初始化WebSocket连接
})
</script>

<style scoped>
.diagnostics-container {
  padding: 20px;
}
</style>
