<template>
  <div class="number-keyboard">
    <div class="keyboard-container">
      <!-- 数字按钮 1-9 -->
      <div class="number-grid">
        <button
          v-for="number in 9"
          :key="number"
          class="key number-key"
          @click="onNumberPress(number)"
        >
          {{ number }}
        </button>

        <!-- 功能键和0 -->
        <button class="key function-key clear-key" @click="onClearPress">
          <span class="function-text">清空</span>
        </button>
        <button class="key number-key" @click="onNumberPress(0)">0</button>
        <button class="key function-key delete-key" @click="onDeletePress">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M6.707 4.879A3 3 0 018.828 4H15a3 3 0 013 3v6a3 3 0 01-3 3H8.828a3 3 0 01-2.12-.879l-4.415-4.414a1 1 0 010-1.414l4.414-4.414zm4 2.414a1 1 0 00-1.414 1.414L10.586 10l-1.293 1.293a1 1 0 101.414 1.414L12 11.414l1.293 1.293a1 1 0 001.414-1.414L13.414 10l1.293-1.293a1 1 0 00-1.414-1.414L12 8.586l-1.293-1.293z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="function-text">退格</span>
        </button>
      </div>

      <!-- 确认按钮 -->
      <button class="confirm-button" :disabled="!isComplete" @click="onConfirm">确认</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  value: {
    type: Array as () => string[],
    required: true,
  },
  maxLength: {
    type: Number,
    default: 6,
  },
})

const emit = defineEmits(['update:value', 'delete', 'clear', 'confirm'])

const isComplete = computed(() => {
  return props.value.filter((v) => v !== '').length === props.maxLength
})

// 按下数字键
const onNumberPress = (number: number) => {
  const emptyIndex = props.value.findIndex((v) => v === '')
  if (emptyIndex !== -1) {
    const newValue = [...props.value]
    newValue[emptyIndex] = number.toString()
    emit('update:value', newValue)
  }
}

// 按下删除键
const onDeletePress = () => {
  const lastFilledIndex = [...props.value].reverse().findIndex((v) => v !== '')
  if (lastFilledIndex !== -1) {
    const actualIndex = props.value.length - 1 - lastFilledIndex
    const newValue = [...props.value]
    newValue[actualIndex] = ''
    emit('update:value', newValue)
    emit('delete')
  }
}

// 清空所有输入
const onClearPress = () => {
  const newValue = Array(props.maxLength).fill('')
  emit('update:value', newValue)
  emit('clear')
}

// 确认输入
const onConfirm = () => {
  if (isComplete.value) {
    emit('confirm', props.value.join(''))
  }
}
</script>

<style scoped>
.number-keyboard {
  user-select: none;
  -webkit-user-select: none;
  width: 100%;
  margin-top: 1rem;
}

.keyboard-container {
  background-color: #f5f7fa;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.number-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.key {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3.5rem;
  border-radius: 0.75rem;
  font-size: 1.5rem;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.number-key {
  background-color: white;
  color: #1a202c;
  font-weight: 500;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.number-key:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

.number-key:active {
  transform: translateY(1px);
  background-color: #f3f4f6;
}

.function-key {
  background-color: #edf2f7;
  color: #4a5568;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-key {
  background-color: #fee2e2;
  color: #b91c1c;
}

.clear-key:hover {
  background-color: #fecaca;
}

.clear-key:active {
  background-color: #fca5a5;
}

.delete-key {
  background-color: #e0f2fe;
  color: #0369a1;
  font-size: 0.875rem;
}

.delete-key:hover {
  background-color: #bae6fd;
}

.delete-key:active {
  background-color: #7dd3fc;
}

.function-text {
  font-weight: 500;
}

.confirm-button {
  width: 100%;
  padding: 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.confirm-button:active:not(:disabled) {
  background-color: #1d4ed8;
}

.confirm-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}
</style>
