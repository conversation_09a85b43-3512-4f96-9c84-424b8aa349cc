import CryptoJS from 'crypto-js'

/**
 * 輔助函數：格式化日期為 YYYYMMDDHH (使用 UTC 時間避免時區問題)
 * @param {Date} date - 日期對象
 * @returns {string} 格式化後的日期時間字符串
 */
function getFormattedUTCDate(date) {
  const year = date.getUTCFullYear()
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0') // 月份從0開始，需要+1
  const day = date.getUTCDate().toString().padStart(2, '0')
  const hours = date.getUTCHours().toString().padStart(2, '0')
  return `${year}${month}${day}${hours}`
}

/**
 * 生成 6 位數字代碼 ("加密"過程)
 * @param {string} machineCode - 代表機器的唯一標識符 (例如 MAC 地址、UUID 等)
 * @param {Date} date - 用於生成代碼的時間點 (通常是當前時間)
 * @returns {Promise<string>} 解析為 6 位數字代碼字符串的 Promise
 */
export async function generateCode(machineCode: string, date: Date) {
  if (typeof machineCode !== 'string' || machineCode.length === 0) {
    throw new Error('Machine code must be a non-empty string.')
  }
  if (!(date instanceof Date)) {
    throw new Error('Date must be a valid Date object.')
  }

  const formattedDate = getFormattedUTCDate(date)
  const combinedInput = machineCode + ':' + formattedDate

  try {
    // 使用 crypto-js 计算 SHA-256 哈希
    const hash = CryptoJS.SHA256(combinedInput)

    // 获取哈希值的十六进制字符串
    const hashHex = hash.toString()

    // 取前8个字符（32位）转换为数字
    const hashInt = parseInt(hashHex.substring(0, 8), 16)

    // 取模得到 6 位数范围内的数字
    const codeValue = hashInt % 1000000

    // 格式化为 6 位字符串 (前面补零)
    const codeString = codeValue.toString().padStart(6, '0')

    return codeString
  } catch (error) {
    console.error('Error generating code:', error)
    throw error
  }
}

/**
 * 驗證提供的代碼是否有效 ("解密"/驗證過程)
 * @param {string} machineCode - 代表機器的唯一標識符 (必須與生成時相同)
 * @param {Date} date - 用於驗證的時間點 (用於確定哪個時間段的代碼是有效的)
 * @param {string} providedCode - 用戶提供的 6 位數字代碼
 * @returns {Promise<boolean>} 解析為 true (如果代碼有效) 或 false (如果無效) 的 Promise
 */
export async function verifyCode(machineCode: string, date: Date, providedCode: string) {
  if (typeof providedCode !== 'string' || !/^\d{6}$/.test(providedCode)) {
    console.warn('Provided code is not a 6-digit string.')
    return false // 無效格式直接返回 false
  }
  try {
    const expectedCode = await generateCode(machineCode, date)
    return expectedCode === providedCode
  } catch (error) {
    console.error('Error verifying code:', error)
    return false // 發生錯誤視為驗證失敗
  }
}

// 签名密钥（应该放在环境变量中）
const SIGN_SECRET = '!B26U.T5bvMKm3S'

/**
 * 生成签名
 * @param {string} carId - 车辆ID
 * @param {number} timestamp - 时间戳
 * @returns {string} - 签名
 */
export function generateSign(carId: string, timestamp: number): string {
  if (!carId || typeof timestamp !== 'number') {
    throw new Error('Invalid parameters: carId and timestamp are required')
  }

  // 构造签名消息
  const message = `${carId}:${timestamp}`

  // 生成 HMAC 签名，使用 Base64 编码使结果更短
  return CryptoJS.HmacSHA256(message, SIGN_SECRET)
    .toString(CryptoJS.enc.Base64)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}

/**
 * 验证签名
 * @param {string} sign - 签名
 * @param {string} carId - 车辆ID
 * @param {number} timestamp - 时间戳
 * @returns {boolean} - 验证结果
 */
export function verifySign(
  sign: string,
  carId: string,
  timestamp: number,
): { ok: boolean; msg: string } {
  try {
    if (!sign || !carId || typeof timestamp !== 'number') {
      return { ok: false, msg: '扫码失败，请重新扫码' }
    }

    // 验证时间窗口（5分钟）
    const now = Date.now()
    const timeWindow = 5 * 60 * 1000
    if (Math.abs(now - timestamp) > timeWindow) {
      return { ok: false, msg: '签名已过期' }
    }

    // 生成预期的签名
    const expectedSign = generateSign(carId, timestamp)

    // 比较签名
    return { ok: sign === expectedSign, msg: '扫码成功' }
  } catch (error) {
    return { ok: false, msg: '扫码失败，请重新扫码' }
  }
}
