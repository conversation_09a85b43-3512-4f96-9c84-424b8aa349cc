import { resolve } from 'path'
import { defineConfig } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import svg from 'vite-svg-loader'
import tailwindcss from '@tailwindcss/vite'

// 获取当前执行的npm脚本命令
const isWebBuild = process.env.npm_lifecycle_event === 'build:web'
// 主进程配置, 如果只打包web则不使用这两个配置
const mainConfig = {
  main: {
    build: {
      rollupOptions: {
        external: ['electron'],
      },
    },
  },
  preload: {
    build: {
      rollupOptions: {
        external: ['electron'],
      },
    },
  },
}

export default defineConfig({
  ...(isWebBuild ? {} : mainConfig),
  renderer: {
    resolve: {
      alias: {
        '@': resolve('src/renderer/src'),
      },
    },
    server: {
      host: true,
      proxy: {
        '/wcs-server': process.env.VITE_API_URL || 'http://************:7080',
        '/dispatch-server': process.env.VITE_API_URL || 'http://************:7080',
      },
    },
    plugins: [vue(), svg(), tailwindcss()],
  },
})
