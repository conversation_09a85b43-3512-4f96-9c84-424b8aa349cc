<template>
  <div class="w-full h-full flex flex-col justify-between overflow-hidden">
    <div class="flex flex-col h-full">
      <MenuView :car-id="carId" @select="handleGoodsSelect" />
    </div>
    <CartView />
  </div>
</template>

<script lang="ts" setup>
import MenuView from './menu-view/index.vue'
import CartView from './cart-view/index.vue'
import { useRouter } from 'vue-router'
import type { GoodsItem } from '../types'
import { ref } from 'vue'
const router = useRouter()
const carId = ref(localStorage.getItem('carId'))
// 商品选择
function handleGoodsSelect(goods: GoodsItem) {
  handleGoodsDetails()
  router.push({
    path: '/consumer/goods-details',
    query: {
      productId: goods.id,
      carId: carId.value,
    },
  })
}

// 模拟请求商品详情
function handleGoodsDetails() {
  if (!carId.value) {
    console.error('没有获取到售卖车id')
    return
  }
}
</script>
