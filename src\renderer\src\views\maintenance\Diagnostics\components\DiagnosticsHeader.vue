<template>
  <div class="diagnostics-header">
    <div class="segmented-container">
      <div class="segmented" :class="{ 'dark-mode': darkMode }">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          class="segmented-item"
          :class="{ active: activeTabIndex === index }"
          @click="setActiveTab(index)"
        >
          {{ tab }}
        </div>
        <div class="segmented-thumb" :style="thumbStyle"></div>
      </div>
    </div>

    <!-- 添加右侧按钮区域 -->
    <div class="right-controls">
      <button class="control-button websocket-button" @click="openWebSocketConfig">
        <span class="button-icon">🔗</span>
        <span class="button-text">WebSocket配置</span>
      </button>
      <button class="control-button keyboard-button" @click="toggleKeyboard">
        <span class="button-icon">⌨️</span>
        <span class="button-text">{{ keyboardVisible ? '关闭虚拟键盘' : '打开虚拟键盘' }}</span>
      </button>
      <button class="control-button kiosk-button" @click="toggleKiosk">
        <span class="button-icon">🖥️</span>
        <span class="button-text">{{ kioskMode ? '退出全屏模式' : '进入全屏模式' }}</span>
      </button>
      <button class="control-button exit-button" @click="exitDiagnostics">
        <span class="button-icon">✖</span>
        <span class="button-text">退出调试模式</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { resetWebSocket } from '@/views/maintenance/hooks/useSocket'
const router = useRouter()
const emit = defineEmits(['change', 'openWebSocketConfig'])
// 定义选项卡内容
const tabs = ['单步调试', '流程调试']
const activeTabIndex = ref(0)
const thumbWidth = ref(0)
const thumbLeft = ref(0)
const darkMode = ref(false) // 可以根据您的主题设置
const keyboardVisible = ref(false) // 记录键盘是否可见
const kioskMode = ref(true) // 记录kiosk全屏模式状态

// 切换激活的选项卡
const setActiveTab = async (index) => {
  activeTabIndex.value = index
  await nextTick()
  updateThumbPosition()
  emit('change', index)
}

// 切换Kiosk全屏模式
const toggleKiosk = async () => {
  if (kioskMode.value) {
    await window.api.exitKiosk()
    kioskMode.value = false
  } else {
    await window.api.enterKiosk()
    kioskMode.value = true
  }
}

// 退出诊断功能
const exitDiagnostics = () => {
  // 确保退出前关闭虚拟键盘
  if (keyboardVisible.value) {
    window.api.closeVirtualKeyboard()
    keyboardVisible.value = false
  }

  // 重置websocket
  resetWebSocket()

  router.replace('/')
}

// 唤醒或关闭虚拟键盘
const toggleKeyboard = () => {
  if (keyboardVisible.value) {
    window.api.closeVirtualKeyboard()
    keyboardVisible.value = false
  } else {
    window.api.openVirtualKeyboard()
    keyboardVisible.value = true
  }
}

// 打开WebSocket配置弹窗
const openWebSocketConfig = () => {
  emit('openWebSocketConfig')
}

// 计算滑块样式
const thumbStyle = computed(() => {
  return {
    width: `${thumbWidth.value}px`,
    transform: `translateX(${thumbLeft.value}px)`,
  }
})

// 更新滑块位置
const updateThumbPosition = () => {
  const activeTab = document.querySelector('.segmented-item.active')
  if (activeTab) {
    thumbWidth.value = activeTab.offsetWidth
    thumbLeft.value = activeTab.offsetLeft
  }
}

// 组件挂载后初始化滑块位置
onMounted(() => {
  updateThumbPosition()
  // 监听窗口大小变化，以更新滑块位置
  window.addEventListener('resize', updateThumbPosition)
})

// 组件卸载前关闭虚拟键盘
onBeforeUnmount(() => {
  if (keyboardVisible.value) {
    window.api.closeVirtualKeyboard()
  }
  window.removeEventListener('resize', updateThumbPosition)
})
</script>

<style scoped>
.diagnostics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.segmented-container {
  padding: 0 10px;
}

.segmented {
  position: relative;
  display: inline-flex;
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  overflow: hidden;
}

.segmented.dark-mode {
  background-color: #303030;
}

.segmented-item {
  position: relative;
  padding: 4px 8px;
  cursor: pointer;
  z-index: 1;
  transition: color 0.3s ease;
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  min-width: 80px;
}

.segmented-item.active {
  color: #fff;
}

.segmented.dark-mode .segmented-item.active {
  color: #1a1a1a;
}

.segmented-thumb {
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 0;
  background-color: #1890ff;
  border-radius: 4px;
  transition:
    transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
    width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  z-index: 0;
}

.segmented.dark-mode .segmented-thumb {
  background-color: #fff;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.auto-test {
  padding: 6px 14px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.auto-test:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.filter-icon {
  cursor: pointer;
}

.right-controls {
  display: flex;
  gap: 20px;
  margin-right: 16px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 4px 16px;
  border-radius: 8px;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 100px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.exit-button {
  color: white;
  background-color: #d9363e;
  border-color: #b7282e;
}

.exit-button:hover {
  background-color: #f5222d;
  border-color: #cf1322;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.keyboard-button {
  color: white;
  background-color: #0d6efd;
  border-color: #0a58ca;
}

.keyboard-button:hover {
  background-color: #0b5ed7;
  border-color: #0a53be;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.kiosk-button {
  color: white;
  background-color: #52c41a;
  border-color: #389e0d;
}

.kiosk-button:hover {
  background-color: #73d13d;
  border-color: #52c41a;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.websocket-button {
  color: white;
  background-color: #722ed1;
  border-color: #531dab;
}

.websocket-button:hover {
  background-color: #9254de;
  border-color: #722ed1;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.button-icon {
  font-size: 20px;
}

.button-text {
  font-size: 16px;
}

@media (max-width: 768px) {
  .right-controls {
    gap: 15px;
  }

  .control-button {
    padding: 8px 14px;
    min-width: 90px;
  }

  .button-text {
    font-size: 14px;
  }
}
</style>
