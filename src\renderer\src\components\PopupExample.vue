<template>
  <div>
    <button class="btn" @click="openPopup">打开弹窗</button>

    <Popup v-model:visible="isPopupVisible">
      <div class="popup-content-example">
        <h2>弹窗标题</h2>
        <p>这是一个居中显示的弹窗，带有全屏遮罩层。</p>
        <p>点击弹窗外部区域可关闭弹窗。</p>
        <button class="close-btn" @click="closePopup">关闭</button>
      </div>
    </Popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Popup from './Popup.vue'

const isPopupVisible = ref(false)

const openPopup = () => {
  isPopupVisible.value = true
}

const closePopup = () => {
  isPopupVisible.value = false
}
</script>

<style scoped>
.btn {
  padding: 8px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.popup-content-example {
  width: 300px;
  padding: 20px;
  text-align: center;
}

.popup-content-example h2 {
  margin-top: 0;
  color: #333;
}

.close-btn {
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
