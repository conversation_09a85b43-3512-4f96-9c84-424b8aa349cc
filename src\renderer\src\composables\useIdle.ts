import { ref, onMounted, onUnmounted } from 'vue'
import { useEventListener } from '@vueuse/core'

export function useIdleTimer(timeout = 120000) {
  const idle = ref(false)
  const lastActive = ref(Date.now())
  let timer: number | null = null

  const reset = () => {
    lastActive.value = Date.now()
    idle.value = false

    if (timer) {
      clearTimeout(timer)
    }

    timer = window.setTimeout(() => {
      idle.value = true
    }, timeout)
  }

  const events = ['mousedown', 'mousemove', 'keydown', 'touchstart', 'click', 'scroll']

  onMounted(() => {
    events.forEach((event) => {
      useEventListener(window, event, reset)
    })
    reset()
  })

  onUnmounted(() => {
    if (timer) {
      clearTimeout(timer)
    }
  })

  return {
    idle,
    lastActive,
    reset,
  }
}
