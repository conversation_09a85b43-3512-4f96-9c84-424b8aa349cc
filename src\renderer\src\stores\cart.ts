// 用户购物车store
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { GoodsDetails, CartItem, ComputedPrice } from '../views/consumer/goods-menu/types'
import Big from 'big.js'

export const useCartStore = defineStore('cart', () => {
  const items = ref<CartItem[]>([])
  const backupItems = ref<CartItem[]>([])
  const computedPrice = ref<ComputedPrice>()

  const backupCart = () => {
    backupItems.value = JSON.parse(JSON.stringify(items.value))
  }

  const restoreCart = () => {
    // 如果备份过 才可以恢复
    if (backupItems.value.length) {
      items.value = JSON.parse(JSON.stringify(backupItems.value))
      backupItems.value = []
    }
  }
  // 选中的商品, 用于商品详情页，在点击商品时，将商品信息存储到selectedGoods中, 预先请求商品详情，减少跳转之后的等待时间
  const selectedGoods = ref<{ [key: string]: Promise<GoodsDetails> }>({})
  // 计算总金额，处理精度问题
  const total = computed(() => {
    return Number(
      items.value
        .reduce((sum, item) => {
          return Big(sum).plus(Big(item.price).times(item.$quantity).toNumber()).toNumber()
        }, 0)
        .toFixed(2),
    )
  })

  // 计算总数量
  const totalCount = computed(() => {
    return Number(
      items.value
        .reduce((sum, item) => {
          return sum + item.$quantity
        }, 0)
        .toFixed(2),
    )
  })

  // 添加商品到购物车
  function addItem(item: CartItem) {
    const existingItem = items.value.find(
      (i) =>
        i.id === item.id &&
        JSON.stringify(i.$specification) === JSON.stringify(item.$specification),
    )

    if (existingItem) {
      existingItem.$quantity += item.$quantity
    } else {
      items.value.push({ ...item, $quantity: item.$quantity })
    }
  }

  // 从购物车减少商品数量
  function decreaseItem(item: CartItem, onLastItem?: () => Promise<boolean>) {
    const index = items.value.findIndex(
      (i) =>
        i.id === item.id &&
        JSON.stringify(i.$specification) === JSON.stringify(item.$specification),
    )
    if (index === -1) return

    if (item.$quantity === 1 && onLastItem) {
      // 当数量为1时，调用外部回调决定是否删除
      onLastItem().then((shouldRemove) => {
        if (shouldRemove) {
          items.value.splice(index, 1)
        }
      })
    } else if (item.$quantity > 1) {
      item.$quantity--
    }
  }

  // 直接移除商品
  function removeItem(item: CartItem) {
    const index = items.value.findIndex(
      (i) =>
        i.id === item.id &&
        JSON.stringify(i.$specification) === JSON.stringify(item.$specification),
    )
    if (index !== -1) {
      items.value.splice(index, 1)
    }
  }

  // 清空购物车
  function clearCart() {
    items.value = []
  }

  // 设置商品详情
  function setGoodsDetails(goodsId: string, promise: Promise<GoodsDetails>) {
    selectedGoods.value[goodsId] = promise
  }

  // 获取商品详情
  function getGoodsDetails(id: string | number) {
    return selectedGoods.value[id]
  }

  function setComputedPrice(price: ComputedPrice) {
    // 原总价
    const originalTotalPrice = price.goodsInfo.reduce((sum, item) => {
      return Big(sum).plus(Big(item.price).times(item.number).toNumber()).toNumber()
    }, 0)
    // 支付金额
    const payPrice = price.compensationTotalPrice || price.totalPrice || originalTotalPrice
    computedPrice.value = {
      // 原总价
      originalTotalPrice,
      ...price,
      // 活动优惠金额 = 原总价 - 现总价 - 优惠券金额
      activityDiscountAmount: Big(originalTotalPrice)
        .minus(price.totalPrice)
        .minus(price.couponPrice || 0)
        .toNumber(),
      payPrice,
      // 总优惠金额 = 原总价 - 现总价
      totalDiscountAmount: Big(originalTotalPrice).minus(payPrice).toNumber(),
    }

    const goodsPriceMap = price.goodsInfo.reduce(
      (map, item) => {
        const originalTotalPrice = Big(item.price).times(item.number).toNumber()
        map[item.skuId] = {
          promotionLabel: item.promotionLabel,
          discountPrice: item.compensationLowestPrice || item.lowestPrice || originalTotalPrice,
          originalTotalPrice,
        }
        return map
      },
      {} as Record<
        string,
        { promotionLabel: string; discountPrice: number; originalTotalPrice: number }
      >,
    )

    items.value.forEach((item) => {
      item.discountPrice = goodsPriceMap[item.$skuId!]?.discountPrice
      item.promotionLabel = goodsPriceMap[item.$skuId!]?.promotionLabel
      item.price = goodsPriceMap[item.$skuId!]?.originalTotalPrice
    })
  }

  return {
    items,
    selectedGoods,
    computedPrice,
    total,
    totalCount,
    addItem,
    decreaseItem,
    setComputedPrice,
    removeItem,
    clearCart,
    backupCart,
    restoreCart,
    setGoodsDetails,
    getGoodsDetails,
  }
})
