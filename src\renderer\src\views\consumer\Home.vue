<template>
  <MenuList v-if="online" />
  <Offline v-else />
</template>
<script lang="ts" setup>
import MenuList from './goods-menu/components/menu.vue'
import Offline from '../../components/offline.vue'
import { useRoute } from 'vue-router'
import { useOnline } from '@vueuse/core'

const route = useRoute()
const online = useOnline()

const carId = (route.query.carId as string) || import.meta.env.VITE_CAR_ID
if (carId && !localStorage.getItem('carId')) {
  localStorage.setItem('carId', carId)
}
</script>
