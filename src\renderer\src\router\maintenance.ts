// import MaintenanceLayout from '../layouts/MaintenanceLayout.vue'

export default [
  {
    path: '/maintenance',
    // component: MaintenanceLayout,
    children: [
      {
        path: 'login',
        name: 'MaintenanceLogin',
        component: () => import('../views/maintenance/Login.vue'),
        meta: { title: '运维登录' },
      },
      {
        path: 'diagnostics',
        name: 'MaintenanceDiagnostics',
        component: () => import('../views/maintenance/Diagnostics/index.vue'),
        meta: {
          title: '机器诊断',
          requiresAuth: true,
        },
      },
      /*   {
        path: 'settings',
        name: 'MaintenanceSettings',
        component: () => import('../views/maintenance/Settings.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true,
        },
      }, */
    ],
  },
]
