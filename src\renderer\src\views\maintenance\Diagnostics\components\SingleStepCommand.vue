<template>
  <div class="debug-container">
    <!-- 左侧侧边栏 -->
    <div class="sidebar-wrapper">
      <div class="sidebar-card">
        <div
          v-for="(group, index) in deviceGroups"
          :key="index"
          class="sidebar-item"
          :class="{ active: activeGroup === index }"
          @click="scrollToGroup(index)"
        >
          <span class="item-text">{{ group.groupName }}</span>
          <div class="item-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 右侧内容面板 -->
    <div ref="contentPanel" class="content-panel" @scroll="handleScroll">
      <div
        v-for="(group, index) in deviceGroups"
        :key="index"
        :ref="
          (el) => {
            if (el) groupRefs[index] = el
          }
        "
        class="group-section"
      >
        <h2 class="group-title">{{ group.groupName }}</h2>
        <div class="controls-container">
          <div v-for="control in group.controls" :key="control.id" class="control-item">
            <div class="control-label">{{ control.label }}</div>
            <div v-if="control.type === 'switch'" class="control-switch">
              <Switcher
                v-model="controlStates[control.id]"
                :active-text="control.valueDescription['1']"
                :inactive-text="control.valueDescription['0']"
                active-color="#13ce66"
                inactive-color="#909399"
                :disabled="controlLoading[control.id]"
                @change="handleControlChange(control.id, $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import deviceData from '../StepCommand.json'
import Switcher from './Switcher.vue'
import { useWebSocketConfig } from '@/views/maintenance/hooks/useWebSocketConfig'
import { toast } from '@/libs/toast'

const { send } = useWebSocketConfig()
// 设备组数据
const deviceGroups = ref(deviceData)

// 控制器状态
const controlStates = reactive({})

// 控制器loading状态
const controlLoading = reactive({})

// 初始化控制器状态
deviceGroups.value.forEach((group) => {
  group.controls.forEach((control) => {
    controlStates[control.id] = false
    controlLoading[control.id] = false
  })
})

// 当前激活的组索引
const activeGroup = ref(0)

// 组元素引用集合
const groupRefs = reactive([])
const contentPanel = ref(null)

// 是否正在滚动 (避免滚动事件循环)
const isScrolling = ref(false)

// 控制器状态变更处理
const handleControlChange = (id, value) => {
  // 如果正在loading，直接返回
  if (controlLoading[id]) {
    return
  }

  console.log(`控制器 ${id} 状态变更为: ${value}`)

  // 保存旧值，用于失败时回滚
  const oldValue = !value

  // 设置loading状态
  controlLoading[id] = true

  try {
    const res = send(
      JSON.stringify({
        type: 'deviceDebug',
        machineNumber: '1',
        data: {
          deviceNumber: id.toString(),
          deviceParams: value ? '1' : '0',
        },
      }),
    )

    if (res) {
      toast('命令发送成功')
    } else {
      // 发送失败，回滚switch状态
      controlStates[id] = oldValue
      toast('命令发送失败，状态已回滚', 'error')
    }
  } catch (error) {
    // 异常情况也要回滚
    controlStates[id] = oldValue
    toast('命令发送异常，状态已回滚', 'error')
  } finally {
    // 无论成功失败都要清除loading状态
    controlLoading[id] = false
  }
}

// 滚动到指定组
const scrollToGroup = (index) => {
  if (isScrolling.value) return

  isScrolling.value = true
  activeGroup.value = index

  if (groupRefs[index]) {
    groupRefs[index].scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })

    // 滚动完成后重置标志
    setTimeout(() => {
      isScrolling.value = false
    }, 500)
  }
}

// 处理滚动事件，更新当前激活的组
const handleScroll = () => {
  if (isScrolling.value) return

  // 找到当前可见的组
  for (let i = 0; i < groupRefs.length; i++) {
    const el = groupRefs[i]
    if (!el) continue

    const rect = el.getBoundingClientRect()
    const elTop = rect.top
    const panelTop = contentPanel.value.getBoundingClientRect().top
    const relativeTop = elTop - panelTop

    // 如果元素顶部在视口内或刚好在顶部之上
    if (relativeTop <= 50 && relativeTop + rect.height > 0) {
      activeGroup.value = i
      break
    }
  }
}

onMounted(() => {
  // 确保所有引用都已填充
  groupRefs.length = deviceGroups.value.length
})
</script>

<style scoped>
.debug-container {
  display: flex;
  height: calc(100vh - 120px);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-wrapper {
  width: 180px;
  background-color: #f5f7fa;
  padding: 20px 15px;
  overflow-y: auto;
}

.sidebar-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.sidebar-item {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  border-bottom: 1px solid #f0f2f5;
}

.sidebar-item:last-child {
  border-bottom: none;
}

.sidebar-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.sidebar-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
}

.item-text {
  display: block;
  line-height: 1.4;
}

.item-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
}

.sidebar-item.active .item-indicator {
  background-color: #409eff;
}

.content-panel {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.group-section {
  padding: 25px 0;
  /* 移除分段虚线 */
  margin-bottom: 0;
}

.group-title {
  margin-top: 0;
  margin-bottom: 20px;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #ebeef5; /* 仅保留标题下方的细线 */
  font-size: 18px;
  color: #303133;
  letter-spacing: 0.5px;
  /* 移除背景色和文字阴影 */
}

.controls-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 15px;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f9fafc; /* 浅色背景代替边框 */
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  /* 移除左侧彩色边框 */
}

.control-label {
  font-weight: 500; /* 减轻字重 */
  color: #303133;
  font-size: 14px; /* 减小文字大小 */
}

.control-switch {
  min-width: 140px;
}

@media (max-width: 768px) {
  .debug-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar-wrapper {
    width: 100%;
    padding: 15px;
  }

  .sidebar-card {
    display: flex;
    overflow-x: auto;
  }

  .sidebar-item {
    padding: 10px 15px;
    white-space: nowrap;
    border-bottom: none;
    border-right: 1px solid #f0f2f5;
    flex-shrink: 0;
  }

  .sidebar-item:last-child {
    border-right: none;
  }

  .item-indicator {
    left: 0;
    right: 0;
    top: auto;
    bottom: 0;
    width: auto;
    height: 3px;
  }

  .controls-container {
    grid-template-columns: 1fr;
  }
}

/* 针对户外强光环境优化的额外样式 */
@media (max-width: 1024px) {
  .control-item {
    background-color: #f0f2f5;
  }
}

/* 平滑的悬停效果 */
.control-item:hover {
  background-color: #f0f2f5;
  transition: background-color 0.3s ease;
  /* 移除抬升效果 */
}
</style>
