{"name": "electron-app", "version": "1.0.9", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:web": "electron-vite build", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "serve": "serve ./dist -p 17922"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@vueuse/core": "^12.2.0", "auto-launch": "^5.0.6", "big.js": "^7.0.1", "crypto-js": "^4.2.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "mitt": "^3.0.1", "ping": "^0.4.4", "pinia": "^2.0.0", "qrcode": "^1.5.4", "qs": "^6.13.1", "regedit": "^5.1.4", "swiper": "^11.2.6", "vue-router": "^4.0.0", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.10.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20.14.8", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "cross-env": "^7.0.3", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.3.2", "serve": "^14.2.4", "tailwindcss": "^4.1.4", "typescript": "^5.6.2", "vite": "^5.3.1", "vite-svg-loader": "^5.1.0", "vue": "^3.5.13", "vue-tsc": "^2.0.29"}}